'use client';

import type { z } from 'zod';

import { skipToken, useQuery } from '@qantasexperiences/react-query/react';
import { removeNullAndUndefinedEntries } from '@qantasexperiences/utils/general';
import { safePartialParseArray } from '@qantasexperiences/utils/zod';

import type { HolidaysPropertiesSearchSortOption } from '../../../types';
import {
  getHolidaysPropertiesWithOfferResponseSchema,
  holidaysPropertiesWithOfferHookSearchParamsSchema,
  propertyWithOfferSchema,
} from '../../../schemas';
import { useFetchAPI } from '../../fetchApi';

type NullableEntries<T> = { [P in keyof T]: T[P] | null | undefined };

interface RequiredHolidaysPropertiesSearchOptions {
  adults: number;
  children: number;
  departureDate: string;
  destinationCode: string;
  destinationName: string;
  infants: number;
  limit: number;
  originCode: string;
  page: number;
  returnDate: string;
}

interface OptionalHolidaysPropertiesSearchOptions {
  maxOfferPriceAud?: number | null;
  minOfferPriceAud?: number | null;
  propertyRatings?: number[] | null;
  regionIds?: number[] | null;
  sort?: HolidaysPropertiesSearchSortOption | null;
}

type HolidaysPropertiesSearchOptions = RequiredHolidaysPropertiesSearchOptions &
  OptionalHolidaysPropertiesSearchOptions;

export type UseHolidaysPropertiesWithOfferOptions =
  NullableEntries<HolidaysPropertiesSearchOptions>;

export type HolidaysPropertyWithOffer = z.infer<typeof propertyWithOfferSchema>;

type ResponseMeta = z.infer<typeof getHolidaysPropertiesWithOfferResponseSchema>['meta'];

export interface HolidaysPropertiesMeta extends ResponseMeta {
  searchOptions: HolidaysPropertiesSearchOptions;
}

export type HolidaysPropertiesQueryResult<T = HolidaysPropertyWithOffer> = {
  meta: HolidaysPropertiesMeta;
  promoCodes: string[];
  propertiesWithOffer: T[];
} | null;

export type HolidaysPropertiesQueryReturn =
  HolidaysPropertiesQueryResult<HolidaysPropertyWithOffer>;

export interface UseHolidaysPropertiesWithOfferReturn {
  data: HolidaysPropertiesQueryReturn | undefined;
  isLoading: boolean;
}

export const useHolidaysPropertiesWithOffer = (
  options: UseHolidaysPropertiesWithOfferOptions,
  config?: {
    onError?: (error: unknown, info: { searchOptions: HolidaysPropertiesSearchOptions }) => void;
    onQueryStart?: () => void;
  },
): UseHolidaysPropertiesWithOfferReturn => {
  const fetchApi = useFetchAPI();

  const { success: isValid } = holidaysPropertiesWithOfferHookSearchParamsSchema.safeParse(options);

  const results = useQuery<HolidaysPropertiesQueryReturn>({
    queryKey: ['getHolidayPropertiesWithOffer', options],
    retry: false,
    placeholderData: (previousData) => previousData,
    queryFn: isValid
      ? async ({ queryKey }): Promise<HolidaysPropertiesQueryReturn> => {
          config?.onQueryStart?.();
          // Assert this as HolidaysPropertiesSearchParams (which has all required fields) as this is only called when
          // 'hasAllRequiredSearchParams' is true which means there are no undefined/null values for the required fields.
          const [, searchOptions] = queryKey as [string, HolidaysPropertiesSearchOptions];
          try {
            const response = await fetchApi.get('get-holidays-properties-with-offer', {
              searchParams: removeNullAndUndefinedEntries(
                holidaysPropertiesWithOfferHookSearchParamsSchema.parse(searchOptions),
              ),
            });
            const data = await response.json();

            const {
              promoCodes,
              propertiesWithOffer: rawProperties,
              meta,
            } = getHolidaysPropertiesWithOfferResponseSchema.parse(data);

            const { validItems } = safePartialParseArray(rawProperties, propertyWithOfferSchema);

            return {
              promoCodes,
              propertiesWithOffer: validItems,
              meta: { ...meta, searchOptions },
            };
          } catch (error) {
            config?.onError?.(error, { searchOptions });
            throw error;
          }
        }
      : skipToken,
  });

  return {
    data: results.data,
    isLoading: results.isLoading || (results.isFetching && results.isPlaceholderData),
  };
};
