import { http, HttpResponse } from 'msw';
import { setupServer } from 'msw/node';

import { serverEnv } from '@qantasexperiences/env/server';

import type { GetCheapestPackageOffersOptions } from './getCheapestPackageOffers';
import { getSearchParams } from '../../../utils/request';
import { getCheapestPackageOffers } from './getCheapestPackageOffers';

const cheapestOfferParamsCheck = jest.fn();

const DEFAULT_PAVA_RESPONSE = {
  package_offers: [
    {
      property_id: '1',
      hotel_offer: { id: '9' },
      charges: { total: { amount: '100' } },
      room_type_name: 'standard',
      rating: 1,
      rating_type: 'AAA',
      images: null,
      main_image: null,
      name: 'hotel one',
    },
    {
      property_id: '2',
      hotel_offer: { id: '8' },
      charges: { total: { amount: '200' } },
      room_type_name: 'standard',
      rating: 2,
      rating_type: 'SELF_RATED',
      images: null,
      main_image: null,
      name: 'hotel two',
    },
    {
      property_id: '3',
      hotel_offer: { id: '7' },
      charges: { total: { amount: '50' } },
      room_type_name: 'standard',
      rating: 3,
      rating_type: 'AAA',
      images: null,
      main_image: null,
      name: 'hotel three',
    },
  ],
  flight_options: {
    outbound_journeys: [
      {
        flight_segments: [
          { promotion_code: null },
          { promotion_code: null },
          { promotion_code: 'FAFF' },
          { promotion_code: null },
        ],
      },
    ],
    inbound_journeys: [{ flight_segments: [{ promotion_code: 'SALE' }] }],
  },
  meta: {
    pagination: { total_package_offers: 30 },
    price_range: { min_aud: 100, max_aud: 500 },
  },
};

const DEFAULT_PARSED_RESPONSE = {
  packageOffers: [
    {
      charges: { total: '100' },
      propertyId: '1',
      offerId: '9',
      roomTypeName: 'standard',
      rating: 1,
      ratingType: 'AAA',
      images: null,
      mainImage: null,
      name: 'hotel one',
    },
    {
      charges: { total: '200' },
      propertyId: '2',
      offerId: '8',
      roomTypeName: 'standard',
      rating: 2,
      ratingType: 'SELF_RATED',
      images: null,
      mainImage: null,
      name: 'hotel two',
    },
    {
      charges: { total: '50' },
      propertyId: '3',
      offerId: '7',
      roomTypeName: 'standard',
      rating: 3,
      ratingType: 'AAA',
      images: null,
      mainImage: null,
      name: 'hotel three',
    },
  ],
  promoCodes: ['SALE', 'FAFF'],
  meta: {
    totalPackageOffers: 30,
    minPrice: 100,
    maxPrice: 500,
  },
};

const handler = http.get(
  `${serverEnv.PACKAGE_AVAILABILITY_API_BASE_PATH}/v2/packages/cheapest_offers`,
  ({ request }) => {
    const params = getSearchParams(request);
    cheapestOfferParamsCheck(params);

    if (params.adults === '0') {
      return new HttpResponse(null, { status: 500 });
    }

    if (params.destination_code === 'NO_FILTER_META') {
      return HttpResponse.json({
        ...DEFAULT_PAVA_RESPONSE,
        meta: { pagination: DEFAULT_PAVA_RESPONSE.meta.pagination },
      });
    }

    return HttpResponse.json(DEFAULT_PAVA_RESPONSE);
  },
);
const server = setupServer(handler);

const BASE_OPTIONS = {
  departureDate: '2024-11-11',
  destinationCode: 'SYD',
  propertyIds: ['1', '2', '3'],
  infants: 0,
  originCode: 'MEL',
  returnDate: '2024-11-22',
  adults: 2,
  children: 0,
  page: 2,
  limit: 20,
  minOfferPriceAud: 300,
  maxOfferPriceAud: 500,
  propertyRatings: [3, 4, 5],
  sort: 'price_asc',
} satisfies Omit<GetCheapestPackageOffersOptions, 'tenant'>;

const QANTAS_OPTIONS = {
  ...BASE_OPTIONS,
  tenant: { brand: 'qantas', channel: 'holidays' },
} satisfies GetCheapestPackageOffersOptions;

describe('getCheapestPackageOffers', () => {
  beforeAll(() => {
    server.listen();
  });

  afterEach(() => {
    server.resetHandlers();
    jest.clearAllMocks();
  });

  afterAll(() => {
    server.close();
  });

  it.each([
    {
      tenant: { brand: 'qantas', channel: 'holidays' },
      tenantParams: { brand: 'qantas', channel: 'qantas_holidays' },
    },
    {
      tenant: { brand: 'jetstar', channel: 'holidays' },
      tenantParams: { brand: 'jetstar', channel: 'jetstar_holidays' },
    },
  ] as const)(
    'sends a request to the cheapest offers endpoint with the params $tenantParams included for the tenant $tenant',
    async ({ tenant, tenantParams }) => {
      await getCheapestPackageOffers({ ...BASE_OPTIONS, tenant });

      expect(cheapestOfferParamsCheck).toHaveBeenNthCalledWith(1, {
        departure_date: '2024-11-11',
        destination_code: 'SYD',
        property_ids: '1,2,3',
        infants: '0',
        origin_code: 'MEL',
        return_date: '2024-11-22',
        adults: '2',
        children: '0',
        travel_class: 'economy',
        limit: '20',
        page: '2',
        min_offer_price_aud: '300',
        max_offer_price_aud: '500',
        has_property_ratings: '3,4,5',
        sort: 'price_asc',
        ...tenantParams,
      });
    },
  );

  it('sends a request without has_property_ratings when propertyRatings is not provided', async () => {
    const QANTAS_OPTIONS_WITHOUT_PROPERTY_RATINGS = {
      ...QANTAS_OPTIONS,
      propertyRatings: undefined,
    };
    await getCheapestPackageOffers(QANTAS_OPTIONS_WITHOUT_PROPERTY_RATINGS);

    const [actualParams] = cheapestOfferParamsCheck.mock.calls[0] as [
      GetCheapestPackageOffersOptions,
    ];
    expect(actualParams).not.toHaveProperty('has_property_ratings');
  });

  it('sends a request with a sort value of "default" if it is not provided', async () => {
    await getCheapestPackageOffers({ ...QANTAS_OPTIONS, sort: undefined });

    expect(cheapestOfferParamsCheck).toHaveBeenCalledWith(
      expect.objectContaining({ sort: 'default' }),
    );
  });

  it('returns the parsed offers, promoCodes and meta info from PAVA', async () => {
    const response = await getCheapestPackageOffers(QANTAS_OPTIONS);

    expect(response).toStrictEqual(DEFAULT_PARSED_RESPONSE);
  });

  it('returns parsed data even when filter meta data is missing', async () => {
    const response = await getCheapestPackageOffers({
      ...QANTAS_OPTIONS,
      destinationCode: 'NO_FILTER_META',
    });

    expect(response).toStrictEqual({
      ...DEFAULT_PARSED_RESPONSE,
      meta: {
        totalPackageOffers: 30,
        minPrice: undefined,
        maxPrice: undefined,
      },
    });
  });
});
