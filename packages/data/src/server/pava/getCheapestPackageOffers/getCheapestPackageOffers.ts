import type { Tenant } from '@qantasexperiences/tenants';
import { serverEnv } from '@qantasexperiences/env/server';
import { removeNullAndUndefinedEntries } from '@qantasexperiences/utils/general';

import type { HolidaysPropertiesSearchSortOption } from '../../../types';
import { getCheapestPackageOfferIncreasedRangeSchema } from '../../../schemas';
import { kyInstance } from '../../../utils/ky';
import { getBrandParams } from '../utils';

export interface GetCheapestPackageOffersOptions {
  adults: number;
  children: number;
  departureDate: string;
  destinationCode: string;
  infants: number;
  // Number of results per page
  limit: number;
  maxOfferPriceAud?: number;
  minOfferPriceAud?: number;
  originCode: string;
  page: number;
  propertyIds: string[];
  propertyRatings?: number[];
  regionIds?: number[];
  returnDate: string;
  sort?: HolidaysPropertiesSearchSortOption;
  tenant: Tenant;
  travelClass?: string;
}

export const getCheapestPackageOffers = async ({
  tenant,
  propertyIds,
  adults,
  children,
  infants,
  departureDate,
  regionIds,
  returnDate,
  travelClass = 'economy',
  destinationCode,
  originCode,
  page,
  limit,
  minOfferPriceAud,
  maxOfferPriceAud,
  propertyRatings,
  sort = 'default',
}: GetCheapestPackageOffersOptions) => {
  const resp = await kyInstance.get(
    `${serverEnv.PACKAGE_AVAILABILITY_API_BASE_PATH}/v2/packages/cheapest_offers`,
    {
      timeout: 20000,
      searchParams: removeNullAndUndefinedEntries({
        property_ids: propertyIds.join(','),
        adults,
        children,
        infants,
        departure_date: departureDate,
        return_date: returnDate,
        travel_class: travelClass,
        destination_code: destinationCode,
        origin_code: originCode,
        min_offer_price_aud: minOfferPriceAud,
        max_offer_price_aud: maxOfferPriceAud,
        has_property_ratings: propertyRatings?.join(','),
        ...getBrandParams(tenant.brand),
        page,
        limit,
        sort,
        region_ids: regionIds?.join(','),
      }),
    },
  );

  const data = await resp.json();

  return getCheapestPackageOfferIncreasedRangeSchema.parse(data);
};
