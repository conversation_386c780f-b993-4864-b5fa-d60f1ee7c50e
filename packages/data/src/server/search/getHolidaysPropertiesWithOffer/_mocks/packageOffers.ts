import type { PavaPackageOffer, PavaPackageOfferIncreasedRange } from '../../../../schemas';

export const packageOfferIncreasedRange1 = {
  propertyId: '1',
  offerId: 'offer1',
  charges: { total: '100' },
  roomTypeName: 'standard',
  rating: 3.5,
  ratingType: 'AAA',
  images: null,
  mainImage: null,
  name: 'Property 1',
} satisfies PavaPackageOfferIncreasedRange;

export const packageOfferIncreasedRange2 = {
  propertyId: '2',
  offerId: 'offer2',
  charges: { total: '50' },
  roomTypeName: 'standard',
  rating: 4,
  ratingType: 'SELF_RATED',
  images: null,
  mainImage: null,
  name: 'Test Property 2',
} satisfies PavaPackageOfferIncreasedRange;

export const packageOffer1 = {
  offerId: packageOfferIncreasedRange1.offerId,
  propertyId: packageOfferIncreasedRange1.propertyId,
  charges: packageOfferIncreasedRange1.charges,
  roomTypeName: packageOfferIncreasedRange1.roomTypeName,
} satisfies PavaPackageOffer;

export const packageOffer2 = {
  offerId: packageOfferIncreasedRange2.offerId,
  propertyId: packageOfferIncreasedRange2.propertyId,
  charges: packageOfferIncreasedRange2.charges,
  roomTypeName: packageOfferIncreasedRange2.roomTypeName,
} satisfies PavaPackageOffer;
