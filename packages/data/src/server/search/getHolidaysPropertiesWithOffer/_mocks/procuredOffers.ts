import type { ProcuredOfferWithOffer } from '@qantasexperiences/sanity/api';

export const procuredOffer1 = {
  propertyName: 'Test Property',
  minNumberOfNights: 2,
  packageOfferId: 'a',
  inclusions: [{ title: 'procured offer 1', icon: 'icon' }],
  offerId: 'offer1',
  propertyId: '1',
  terms: 'Terms and Conditions 1',
  extended: false,
  saleEndDate: '2026-12-14T13:37:00Z',
  saleStartDate: '2026-10-14T13:37:00Z',
  travelEndDate: '2027-12-31T13:37:00Z',
  travelStartDate: '2026-12-14T13:37:00Z',
  description: 'Test Description',
  displayTravellerTypes: false,
} satisfies ProcuredOfferWithOffer;

export const procuredOffer2 = {
  propertyName: 'Test Property 2',
  minNumberOfNights: 2,
  packageOfferId: 'b',
  inclusions: [{ title: 'procured offer 2', icon: 'icon' }],
  offerId: 'offer2',
  propertyId: '2',
  terms: 'Terms and Conditions 2',
  extended: false,
  saleEndDate: '2026-12-14T13:37:00Z',
  saleStartDate: '2026-10-14T13:37:00Z',
  travelEndDate: '2027-12-31T13:37:00Z',
  travelStartDate: '2026-12-14T13:37:00Z',
  description: 'Test Description 2',
  displayTravellerTypes: false,
} satisfies ProcuredOfferWithOffer;
