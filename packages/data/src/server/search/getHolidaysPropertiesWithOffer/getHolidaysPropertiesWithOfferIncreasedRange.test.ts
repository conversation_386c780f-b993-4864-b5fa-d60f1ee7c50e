import {
  getHolidaysProcuredOffersByHotelOffer,
  getHolidaysPropertiesByDestination,
} from '@qantasexperiences/sanity/api';

import { getProperties } from '../../ava';
import { getCheapestPackageOffers } from '../../pava';
import {
  avaProperty1,
  avaProperty2,
  packageOffer1,
  packageOffer2,
  packageOfferIncreasedRange1,
  packageOfferIncreasedRange2,
  procuredOffer1,
  sanityProperty1,
  sanityProperty2,
} from './_mocks';
import { getHolidaysPropertiesWithOfferIncreasedRange } from './getHolidaysPropertiesWithOfferIncreasedRange';

jest.mock('@qantasexperiences/logger', () => ({
  logger: { error: jest.fn() },
}));
jest.mock('@qantasexperiences/sanity/api', () => ({
  getHolidaysProcuredOffersByHotelOffer: jest.fn(),
  getHolidaysPropertiesByDestination: jest.fn(),
}));
jest.mock('../../ava', () => ({ getProperties: jest.fn() }));
jest.mock('../../pava', () => ({ getCheapestPackageOffers: jest.fn() }));

describe('getHolidaysPropertiesWIthOffer', () => {
  beforeEach(() => {
    jest.mocked(getProperties).mockResolvedValue([avaProperty1, avaProperty2]);
    jest
      .mocked(getHolidaysPropertiesByDestination)
      .mockResolvedValue([sanityProperty1, sanityProperty2]);
    jest.mocked(getCheapestPackageOffers).mockResolvedValue({
      packageOffers: [packageOfferIncreasedRange1, packageOfferIncreasedRange2],
      promoCodes: ['TEST'],
      meta: {
        totalPackageOffers: 30,
        minPrice: 100,
        maxPrice: 500,
      },
    });
    jest.mocked(getHolidaysProcuredOffersByHotelOffer).mockResolvedValue([procuredOffer1]);
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  const testGetHolidaysPropertiesWithOffer = async () =>
    getHolidaysPropertiesWithOfferIncreasedRange({
      destinationName: 'las-vegas',
      adults: 1,
      children: 0,
      departureDate: '',
      destinationCode: '',
      infants: 1,
      originCode: '',
      returnDate: '',
      page: 1,
      limit: 20,
      regionIds: [123],
      tenant: { brand: 'qantas', channel: 'holidays' },
    });

  it('returns combined PAVA, Sanity, procuredOffers + promoCodes', async () => {
    const results = await testGetHolidaysPropertiesWithOffer();

    expect(results.propertiesWithOffer).toEqual([
      {
        ...sanityProperty1,
        ...packageOfferIncreasedRange1,
        rating: packageOfferIncreasedRange1.rating,
        ratingType: packageOfferIncreasedRange1.ratingType,
        id: packageOfferIncreasedRange1.propertyId,
        images: packageOfferIncreasedRange1.images,
        mainImage: packageOfferIncreasedRange1.mainImage,
        name: packageOfferIncreasedRange1.name,
        offer: packageOffer1,
        procuredOffer: procuredOffer1,
      },
      {
        ...sanityProperty2,
        ...packageOfferIncreasedRange2,
        rating: packageOfferIncreasedRange2.rating,
        ratingType: packageOfferIncreasedRange2.ratingType,
        id: packageOfferIncreasedRange2.propertyId,
        images: packageOfferIncreasedRange2.images,
        mainImage: packageOfferIncreasedRange2.mainImage,
        name: packageOfferIncreasedRange2.name,
        offer: packageOffer2,
        procuredOffer: undefined,
      },
    ]);
    expect(results.promoCodes).toEqual(['TEST']);
  });

  it('returns meta information', async () => {
    const results = await testGetHolidaysPropertiesWithOffer();

    expect(results.meta).toStrictEqual({
      totalPackageOffers: 30,
      minPrice: 100,
      maxPrice: 500,
    });
  });

  it('returns PAVA data when getHolidaysPropertiesByDestination returns an empty array', async () => {
    jest.mocked(getHolidaysPropertiesByDestination).mockResolvedValue([]);

    const results = await testGetHolidaysPropertiesWithOffer();

    expect(results.propertiesWithOffer).toEqual([
      {
        ...packageOfferIncreasedRange1,
        tagline: null,
        tags: null,
        rating: packageOfferIncreasedRange1.rating,
        ratingType: packageOfferIncreasedRange1.ratingType,
        id: packageOfferIncreasedRange1.propertyId,
        images: packageOfferIncreasedRange1.images,
        mainImage: packageOfferIncreasedRange1.mainImage,
        name: packageOfferIncreasedRange1.name,
        offer: packageOffer1,
        procuredOffer: procuredOffer1,
      },
      {
        ...packageOfferIncreasedRange2,
        tagline: null,
        tags: null,
        rating: packageOfferIncreasedRange2.rating,
        ratingType: packageOfferIncreasedRange2.ratingType,
        id: packageOfferIncreasedRange2.propertyId,
        images: packageOfferIncreasedRange2.images,
        mainImage: packageOfferIncreasedRange2.mainImage,
        name: packageOfferIncreasedRange2.name,
        offer: packageOffer2,
        procuredOffer: undefined,
      },
    ]);
  });
});
