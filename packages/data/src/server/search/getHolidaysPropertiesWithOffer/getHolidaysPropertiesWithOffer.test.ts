import {
  getHolidaysProcuredOffersByHotelOffer,
  getHolidaysPropertiesByDestination,
} from '@qantasexperiences/sanity/api';

import { getProperties } from '../../ava';
import { getCheapestPackageOffers } from '../../pava';
import {
  avaProperty1,
  avaProperty2,
  packageOfferIncreasedRange1,
  packageOfferIncreasedRange2,
  procuredOffer1,
  sanityProperty1,
  sanityProperty2,
} from './_mocks';
import { getHolidaysPropertiesWithOffer } from './getHolidaysPropertiesWithOffer';

jest.mock('@qantasexperiences/logger', () => ({
  logger: { error: jest.fn() },
}));
jest.mock('@qantasexperiences/sanity/api', () => ({
  getHolidaysProcuredOffersByHotelOffer: jest.fn(),
  getHolidaysPropertiesByDestination: jest.fn(),
}));
jest.mock('../../ava', () => ({ getProperties: jest.fn() }));
jest.mock('../../pava', () => ({ getCheapestPackageOffers: jest.fn() }));

describe('getHolidaysPropertiesWIthOffer', () => {
  beforeEach(() => {
    jest.mocked(getProperties).mockResolvedValue([avaProperty1, avaProperty2]);
    jest
      .mocked(getHolidaysPropertiesByDestination)
      .mockResolvedValue([sanityProperty1, sanityProperty2]);
    jest.mocked(getCheapestPackageOffers).mockResolvedValue({
      packageOffers: [packageOfferIncreasedRange1, packageOfferIncreasedRange2],
      promoCodes: ['TEST'],
      meta: {
        totalPackageOffers: 30,
        minPrice: 100,
        maxPrice: 500,
      },
    });
    jest.mocked(getHolidaysProcuredOffersByHotelOffer).mockResolvedValue([procuredOffer1]);
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  const testGetHolidaysPropertiesWithOffer = async () =>
    getHolidaysPropertiesWithOffer({
      destinationName: '',
      adults: 1,
      children: 0,
      departureDate: '',
      destinationCode: '',
      infants: 1,
      originCode: '',
      returnDate: '',
      page: 1,
      limit: 20,
      tenant: { brand: 'qantas', channel: 'holidays' },
    });

  it('returns combined property/offer/procuredOffers + promoCodes', async () => {
    const results = await testGetHolidaysPropertiesWithOffer();

    expect(results.propertiesWithOffer).toEqual([
      {
        ...sanityProperty1,
        ...avaProperty1,
        offer: packageOfferIncreasedRange1,
        procuredOffer: procuredOffer1,
      },
      {
        ...sanityProperty2,
        ...avaProperty2,
        offer: packageOfferIncreasedRange2,
      },
    ]);
    expect(results.promoCodes).toEqual(['TEST']);
  });

  it('returns meta information', async () => {
    const results = await testGetHolidaysPropertiesWithOffer();

    expect(results.meta).toStrictEqual({
      totalPackageOffers: 30,
      minPrice: 100,
      maxPrice: 500,
    });
  });

  it('returns empty data if getHolidaysPropertiesByDestination returns an empty array', async () => {
    jest.mocked(getHolidaysPropertiesByDestination).mockResolvedValue([]);

    const results = await testGetHolidaysPropertiesWithOffer();

    expect(results).toStrictEqual({
      propertiesWithOffer: [],
      promoCodes: [],
      meta: { totalPackageOffers: 0, minPrice: undefined, maxPrice: undefined },
    });
  });

  it.each([null, []])(
    'returns empty data but with correct meta information if getProperties returns %s',
    async (avaProperties) => {
      jest.mocked(getProperties).mockResolvedValue(avaProperties);

      const results = await testGetHolidaysPropertiesWithOffer();

      expect(results).toStrictEqual({
        propertiesWithOffer: [],
        promoCodes: [],
        meta: {
          totalPackageOffers: 30,
          minPrice: 100,
          maxPrice: 500,
        },
      });
    },
  );
});
