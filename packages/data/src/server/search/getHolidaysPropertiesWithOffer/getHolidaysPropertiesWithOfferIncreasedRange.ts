import {
  getHolidaysProcuredOffersByHotelOffer,
  getHolidaysPropertiesByDestination,
} from '@qantasexperiences/sanity/api';

import type { CheapestPackageOfferMeta } from '../../../schemas';
import type { GetCheapestPackageOffersOptions } from '../../pava';
import type { HolidaysPropertyWithOffer } from './utils';
import { getCheapestPackageOffers } from '../../pava';
import { combineSearchDataIncreasedRange } from './utils/combineSearchDataIncreasedRange';

interface GetHolidaysPropertiesWithOfferOptions
  extends Omit<GetCheapestPackageOffersOptions, 'propertyIds'> {
  destinationName: string;
}

interface GetHolidaysPropertiesWithOfferReturn {
  meta: CheapestPackageOfferMeta;
  promoCodes: string[];
  propertiesWithOffer: HolidaysPropertyWithOffer[];
}

// Increased Range gets extra data from PAVA instead of AVA
export const getHolidaysPropertiesWithOfferIncreasedRange = async ({
  destinationName,
  ...options
}: GetHolidaysPropertiesWithOfferOptions): Promise<GetHolidaysPropertiesWithOfferReturn> => {
  const { brand } = options.tenant;
  // SANITY PROPERTY DATA
  const sanityProperties = await getHolidaysPropertiesByDestination({
    brand,
    destinationName,
  });

  // PAVA HOTEL OFFER DATA
  const cheapestOffersResponse = await getCheapestPackageOffers({
    ...options,
    propertyIds: sanityProperties.map((property) => property.id),
  });

  const { packageOffers, promoCodes, meta } = cheapestOffersResponse;

  // SANITY PROCURED OFFER DATA
  const procuredOffers = await getHolidaysProcuredOffersByHotelOffer({
    hotelOffers: packageOffers.map((offer) => ({
      offerId: offer.offerId,
      propertyId: offer.propertyId,
    })),
    brand,
  });

  const propertiesWithOffer = combineSearchDataIncreasedRange({
    packageOffers,
    procuredOffers,
    sanityProperties,
  });

  return { propertiesWithOffer, promoCodes, meta };
};
