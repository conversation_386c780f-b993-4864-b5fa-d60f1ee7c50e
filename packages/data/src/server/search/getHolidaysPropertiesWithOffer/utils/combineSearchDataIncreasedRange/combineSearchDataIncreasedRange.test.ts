import {
  packageOffer1,
  packageOffer2,
  packageOfferIncreasedRange1,
  packageOfferIncreasedRange2,
  procuredOffer1,
  procuredOffer2,
  sanityProperty1,
  sanityProperty2,
} from '../../_mocks';
import { combineSearchDataIncreasedRange } from './combineSearchDataIncreasedRange';

describe('combineSearchDataIncreasedRange', () => {
  it('combines data correctly when all data is available', () => {
    const sanityProperties = [sanityProperty1, sanityProperty2];
    const procuredOffers = [procuredOffer1, procuredOffer2];
    const packageOffers = [packageOfferIncreasedRange1, packageOfferIncreasedRange2];

    const result = combineSearchDataIncreasedRange({
      sanityProperties,
      procuredOffers,
      packageOffers,
    });

    expect(result).toEqual([
      {
        ...sanityProperty1,
        ...packageOfferIncreasedRange1,
        id: packageOfferIncreasedRange1.propertyId,
        offer: packageOffer1,
        procuredOffer: procuredOffer1,
      },
      {
        ...sanityProperty2,
        ...packageOfferIncreasedRange2,
        id: packageOfferIncreasedRange2.propertyId,
        offer: packageOffer2,
        procuredOffer: procuredOffer2,
      },
    ]);
  });

  it('keeps a property from packageOffers even if its not found in sanityProperties', () => {
    const sanityProperties = [sanityProperty2];
    const procuredOffers = [procuredOffer1];
    const packageOffers = [packageOfferIncreasedRange1, packageOfferIncreasedRange2];

    const result = combineSearchDataIncreasedRange({
      sanityProperties,
      procuredOffers,
      packageOffers,
    });

    expect(result).toEqual([
      {
        tagline: null,
        tags: null,
        ...packageOfferIncreasedRange1,
        id: packageOfferIncreasedRange1.propertyId,
        offer: packageOffer1,
        procuredOffer: procuredOffer1,
      },
      {
        ...sanityProperty2,
        ...packageOfferIncreasedRange2,
        id: packageOfferIncreasedRange2.propertyId,
        procuredOffer: undefined,
        offer: packageOffer2,
      },
    ]);
  });
});
