import type { HolidaysSanityProperty, ProcuredOfferWithOffer } from '@qantasexperiences/sanity/api';

import type {
  AvaProperty,
  PavaPackageOffer,
  PavaPackageOfferIncreasedRange,
} from '../../../../../schemas';

export interface CommonSearchDataOptions {
  packageOffers: PavaPackageOfferIncreasedRange[];
  procuredOffers: ProcuredOfferWithOffer[] | null;
  sanityProperties: HolidaysSanityProperty[];
}
export interface CombinesAvaPavaSanityDataOptions extends CommonSearchDataOptions {
  avaProperties: AvaProperty[];
}

export interface HolidaysPropertyWithOffer extends HolidaysSanityProperty, AvaProperty {
  offer: PavaPackageOffer;
  procuredOffer?: ProcuredOfferWithOffer;
}

const getProcuredOfferKey = ({
  offerId,
  propertyId,
}: Pick<ProcuredOfferWithOffer, 'offerId' | 'propertyId'>) => `${propertyId}_${offerId}`;

// increased range doesn't need AVA
export const combineSearchDataIncreasedRange = ({
  sanityProperties,
  procuredOffers,
  packageOffers,
}: CommonSearchDataOptions): HolidaysPropertyWithOffer[] => {
  const sanityPropertiesLookup = new Map(
    sanityProperties.map((property) => [property.id, property]),
  );
  const procuredOfferLookup = new Map(
    procuredOffers?.map((procuredOffer) => [getProcuredOfferKey(procuredOffer), procuredOffer]),
  );

  return packageOffers.map<HolidaysPropertyWithOffer>((offer) => {
    const sanityProperty = sanityPropertiesLookup.get(offer.propertyId);
    const procuredOffer = procuredOfferLookup.get(getProcuredOfferKey(offer));

    const {
      rating,
      ratingType,
      images,
      mainImage,
      name,
      offerId,
      propertyId,
      charges,
      roomTypeName,
    } = offer;

    return {
      tagline: null,
      tags: null,
      ...sanityProperty,
      ...offer,
      rating,
      ratingType,
      id: offer.propertyId,
      images,
      mainImage,
      name,
      offer: {
        offerId,
        propertyId,
        charges,
        roomTypeName,
      },
      procuredOffer,
    } satisfies HolidaysPropertyWithOffer;
  });
};
