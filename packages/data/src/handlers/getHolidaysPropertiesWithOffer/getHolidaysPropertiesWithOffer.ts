import { routeHandlerWithLogger } from '@qantasexperiences/logger';

import type { WithTenant } from '../../middleware';
import { withTenant } from '../../middleware';
import { getHolidaysPropertiesWithOfferInputSchema } from '../../schemas';
import {
  getHolidaysPropertiesWithOffer,
  getHolidaysPropertiesWithOfferIncreasedRange,
} from '../../server';
import { getSearchParams } from '../../utils/request';

const GET = async (request: Request & WithTenant) => {
  const { tenant } = request;
  const searchParams = getSearchParams(request);
  const { data, error } = getHolidaysPropertiesWithOfferInputSchema.safeParse(searchParams);

  if (error) {
    return new Response(error.message, { status: 400 });
  }

  let results;
  if (!data.regionIds?.length) {
    results = await getHolidaysPropertiesWithOffer({
      ...data,
      tenant,
    });
  } else {
    results = await getHolidaysPropertiesWithOfferIncreasedRange({
      ...data,
      tenant,
    });
  }

  return Response.json(results);
};

const withMiddlewaresGET = routeHandlerWithLogger(withTenant(GET));

export { withMiddlewaresGET as GET };
