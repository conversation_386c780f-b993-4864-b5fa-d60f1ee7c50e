import { NextRequest } from 'next/server';

import { routeHandlerWithLogger } from '@qantasexperiences/logger';

import {
  getHolidaysPropertiesWithOffer,
  getHolidaysPropertiesWithOfferIncreasedRange,
} from '../../server';
import { GET } from './getHolidaysPropertiesWithOffer';

jest.mock('../../server', () => ({
  getHolidaysPropertiesWithOffer: jest.fn(() => ({})),
  getHolidaysPropertiesWithOfferIncreasedRange: jest.fn(() => ({})),
}));
jest.mock('@qantasexperiences/logger', () => ({
  routeHandlerWithLogger: jest.fn().mockImplementation((handler: typeof GET) => handler),
  logger: { error: jest.fn() },
}));

const testGetHolidaysPropertiesWithOfferGET = () =>
  GET(
    new NextRequest(
      'https://examples.test?adults=1&children=1&departureDate=2025-01-01&destinationCode=SYD&destinationName=sydney&infants=1&originCode=MEL&returnDate=2025-01-08&limit=20&page=1&propertyRatings=1,2,3',
      {
        headers: {
          'X-Brand': 'qantas',
          'X-Channel': 'holidays',
        },
      },
    ),
    {},
  );

const testGetHolidaysPropertiesWithRegionIdGET = () =>
  GET(
    new NextRequest(
      'https://examples.test?adults=1&children=1&departureDate=2025-01-01&destinationCode=SYD&destinationName=sydney&infants=1&originCode=MEL&returnDate=2025-01-08&limit=20&page=1&propertyRatings=1,2,3&regionIds=100,200',
      {
        headers: {
          'X-Brand': 'qantas',
          'X-Channel': 'holidays',
        },
      },
    ),
    {},
  );

describe('GET handler', () => {
  afterEach(() => {
    jest.clearAllMocks();
  });

  it('is wrapped with routeHandlerWithLogger', async () => {
    await testGetHolidaysPropertiesWithOfferGET();

    expect(routeHandlerWithLogger).toHaveBeenNthCalledWith(1, expect.any(Function));
  });

  it('should return 200 with properties when successful', async () => {
    jest.mocked(getHolidaysPropertiesWithOffer).mockResolvedValue({
      // @ts-expect-error don't need actual data
      propertiesWithOffer: [{ id: '1' }],
      promoCodes: ['SALE'],
      meta: {
        totalPackageOffers: 20,
        minPrice: 100,
        maxPrice: 500,
      },
    });
    const resp = await testGetHolidaysPropertiesWithOfferGET();

    expect(resp.status).toBe(200);
    await expect(resp.json()).resolves.toEqual({
      propertiesWithOffer: [{ id: '1' }],
      promoCodes: ['SALE'],
      meta: {
        totalPackageOffers: 20,
        minPrice: 100,
        maxPrice: 500,
      },
    });
  });

  it('should return 200 with increased range properties when region IDs exist', async () => {
    jest.mocked(getHolidaysPropertiesWithOfferIncreasedRange).mockResolvedValue({
      // @ts-expect-error don't need actual data
      propertiesWithOffer: [{ id: '1' }],
      promoCodes: ['SALE'],
      meta: {
        totalPackageOffers: 20,
        minPrice: 100,
        maxPrice: 500,
      },
    });
    const resp = await testGetHolidaysPropertiesWithRegionIdGET();

    expect(resp.status).toBe(200);
    await expect(resp.json()).resolves.toEqual({
      propertiesWithOffer: [{ id: '1' }],
      promoCodes: ['SALE'],
      meta: {
        totalPackageOffers: 20,
        minPrice: 100,
        maxPrice: 500,
      },
    });
  });

  it('returns a 400 when X-Brand header is invalid or missing', async () => {
    const resp1 = await GET(
      new NextRequest('https://examples.test', {
        headers: {
          'X-Channel': 'holidays',
        },
      }),
      {},
    );

    expect(resp1.status).toBe(400);

    const resp2 = await GET(
      new NextRequest('https://examples.test', {
        headers: {
          'X-Channel': 'holidays',
          'X-Brand': 'invalid data',
        },
      }),
      {},
    );

    expect(resp2.status).toBe(400);
  });

  it('returns a 400 when X-Channel header is invalid or missing', async () => {
    const resp1 = await GET(
      new NextRequest('https://examples.test', {
        headers: {
          'X-Brand': 'qantas',
        },
      }),
      {},
    );

    expect(resp1.status).toBe(400);

    const resp2 = await GET(
      new NextRequest('https://examples.test', {
        headers: {
          'X-Channel': 'invalid data',
          'X-Brand': 'qantas',
        },
      }),
      {},
    );

    expect(resp2.status).toBe(400);
  });

  it('should return 400 when input validation fails', async () => {
    const resp = await GET(
      new NextRequest(
        // No departureDate
        'https://examples.test?adults=1&children=1&destinationCode=SYD&destinationName=sydney&infants=1&originCode=MEL&returnDate=2025-01-08&page=1&limit=20',
        {
          headers: {
            'X-Brand': 'qantas',
            'X-Channel': 'holidays',
          },
        },
      ),
      {},
    );

    expect(resp.status).toBe(400);
    await expect(resp.json()).resolves.toEqual([
      {
        code: 'invalid_type',
        expected: 'string',
        message: 'Required',
        path: ['departureDate'],
        received: 'undefined',
      },
    ]);
  });

  it('should return 400 when propertyRatings is the wrong format', async () => {
    const resp = await GET(
      new NextRequest(
        // Incorrect property ratings type
        'https://examples.test?adults=1&children=1&departureDate=2025-01-01&destinationCode=SYD&destinationName=sydney&infants=1&originCode=MEL&returnDate=2025-01-08&page=1&limit=20&propertyRatings=wrong',
        {
          headers: {
            'X-Brand': 'qantas',
            'X-Channel': 'holidays',
          },
        },
      ),
      {},
    );

    expect(resp.status).toBe(400);
    await expect(resp.json()).resolves.toEqual([
      {
        code: 'custom',
        message: 'Malformed data - rating was not a number',
        path: ['propertyRatings'],
      },
    ]);
  });
});
