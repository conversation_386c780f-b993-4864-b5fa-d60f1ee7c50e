import camelcaseKeys from 'camelcase-keys';
import { z } from 'zod';

import { imageSchema } from './image';
import { ratingSchema } from './rating';

export const avaPropertySchema = z
  .object({
    rating: z.number(),
    rating_type: ratingSchema,
    name: z.string(),
    main_image: imageSchema.nullish(),
    images: imageSchema.array().nullish(),
    id: z.string(),
  })
  .transform((data) => camelcaseKeys(data, { deep: true }));

export type AvaProperty = z.infer<typeof avaPropertySchema>;
