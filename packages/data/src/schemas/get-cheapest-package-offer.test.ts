import { z } from 'zod';

import { getCheapestPackageOfferSchema } from './get-cheapest-package-offer';

const mockApiData = {
  package_offers: [
    {
      property_id: 'prop123',
      charges: {
        total: {
          amount: '1500.00',
        },
      },
      hotel_offer: {
        id: 'offer456',
      },
      room_type_name: 'Deluxe Queen',
    },
    {
      property_id: 'prop789',
      charges: {
        total: {
          amount: '1200.50',
        },
      },
      hotel_offer: {
        id: 'offer101',
      },
      room_type_name: 'Standard King',
    },
  ],
  flight_options: {
    outbound_journeys: [
      {
        flight_segments: [{ promotion_code: 'SALE' }, { promotion_code: null }],
      },
      {
        flight_segments: [{ promotion_code: 'EARLYBIRD' }],
      },
    ],
    inbound_journeys: [
      {
        flight_segments: [{ promotion_code: 'SALE' }, { promotion_code: 'FAFF' }],
      },
    ],
  },
  meta: {
    pagination: {
      total_package_offers: 125,
    },
    price_range: {
      min_aud: 1100.0,
      max_aud: 3500.75,
    },
  },
};

describe('getCheapestPackageOfferSchema', () => {
  it('should correctly parse and transform the API response', () => {
    const parsedData = getCheapestPackageOfferSchema.parse(mockApiData);

    expect(parsedData).toHaveProperty('packageOffers');
    expect(parsedData).toHaveProperty('promoCodes');
    expect(parsedData).toHaveProperty('meta');

    expect(parsedData.packageOffers).toEqual([
      {
        propertyId: 'prop123',
        charges: {
          total: '1500.00',
        },
        offerId: 'offer456',
        roomTypeName: 'Deluxe Queen',
      },
      {
        propertyId: 'prop789',
        charges: {
          total: '1200.50',
        },
        offerId: 'offer101',
        roomTypeName: 'Standard King',
      },
    ]);

    expect(parsedData.promoCodes.sort()).toEqual(['EARLYBIRD', 'FAFF', 'SALE'].sort());

    expect(parsedData.meta).toEqual({
      totalPackageOffers: 125,
      minPrice: 1100.0,
      maxPrice: 3500.75,
    });
  });

  it('should handle missing optional price_range in meta', () => {
    const dataWithoutPriceRange = {
      ...mockApiData,
      meta: {
        pagination: {
          total_package_offers: 50,
        },
      },
    };

    const parsedData = getCheapestPackageOfferSchema.parse(dataWithoutPriceRange);

    expect(parsedData.meta).toEqual({
      totalPackageOffers: 50,
      minPrice: undefined,
      maxPrice: undefined,
    });
  });

  it('should handle empty package_offers array', () => {
    const dataWithEmptyOffers = {
      ...mockApiData,
      package_offers: [],
    };

    const parsedData = getCheapestPackageOfferSchema.parse(dataWithEmptyOffers);

    expect(parsedData.packageOffers).toEqual([]);
    expect(parsedData.meta.totalPackageOffers).toBe(125);
  });

  it('should handle empty journeys in flight_options', () => {
    const dataWithEmptyJourneys = {
      ...mockApiData,
      flight_options: {
        outbound_journeys: [],
        inbound_journeys: [],
      },
    };

    const parsedData = getCheapestPackageOfferSchema.parse(dataWithEmptyJourneys);

    expect(parsedData.promoCodes).toEqual([]);
  });

  it('should throw an error for invalid data', () => {
    const invalidData = {
      ...mockApiData,
      package_offers: [
        {
          ...mockApiData.package_offers[0],
          property_id: null,
        },
      ],
    };

    expect(() => getCheapestPackageOfferSchema.parse(invalidData)).toThrow(z.ZodError);
  });

  it('should handle no promo codes', () => {
    const dataWithoutPromoCodes = {
      ...mockApiData,
      flight_options: {
        outbound_journeys: [
          { flight_segments: [{ promotion_code: null }, { promotion_code: undefined }] },
        ],
        inbound_journeys: [{ flight_segments: [{ promotion_code: null }] }],
      },
    };

    const parsedData = getCheapestPackageOfferSchema.parse(dataWithoutPromoCodes);
    expect(parsedData.promoCodes).toEqual([]);
  });
});
