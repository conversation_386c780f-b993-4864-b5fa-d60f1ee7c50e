import { getHolidaysPropertiesWithOfferInputSchema } from './holidays-properties-with-offer';

describe('getHolidaysPropertiesWithOfferInputSchema', () => {
  describe('regionIds', () => {
    const holidayPropertiesSearchParams = {
      adults: 2,
      children: 0,
      departureDate: '2025-09-28',
      destinationCode: '',
      destinationName: '',
      infants: 0,
      originCode: '',
      returnDate: '2025-09-30',
      page: 1,
      limit: 20,
      propertyRatings: '1',
      sort: 'default',
      regionIds: '1,2,3',
    };

    it('should transform a valid comma-separated string to an array of numbers', () => {
      const result = getHolidaysPropertiesWithOfferInputSchema.safeParse(
        holidayPropertiesSearchParams,
      );

      expect(result.data?.regionIds).toMatchObject([1, 2, 3]);
    });

    it('should return an error when string is not a numbers', () => {
      const result = getHolidaysPropertiesWithOfferInputSchema.safeParse({
        ...holidayPropertiesSearchParams,
        regionIds: '1,a,3',
      });

      expect(result.success).toBe(false);
    });
  });
});
