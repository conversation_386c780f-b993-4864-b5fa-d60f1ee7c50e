import { holidaysPropertiesWithOfferHookSearchParamsSchema } from './holidays-properties-with-offer-hook';

describe('holidaysPropertiesWithOfferHookSearchParamsSchema', () => {
  describe('regionIds', () => {
    const holidayPropertiesSearchParams = {
      adults: 2,
      children: 0,
      departureDate: '',
      destinationCode: '',
      destinationName: '',
      infants: 0,
      originCode: '',
      returnDate: '',
      page: 1,
      limit: 20,
      propertyRatings: [1],
      sort: 'default',
      regionIds: [1, 2, 3],
    };

    it('should transform a valid array of numbers to a comma-separated string', () => {
      const result = holidaysPropertiesWithOfferHookSearchParamsSchema.safeParse(
        holidayPropertiesSearchParams,
      );
      expect(result.data?.regionIds).toBe('1,2,3');
    });
  });
});
