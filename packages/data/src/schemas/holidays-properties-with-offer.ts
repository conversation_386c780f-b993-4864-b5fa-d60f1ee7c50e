import { z } from 'zod';

import { HOLIDAYS_PROPERTIES_SEARCH_SORT_OPTIONS } from '../constants';

export const getHolidaysPropertiesWithOfferInputSchema = z.object({
  adults: z.coerce.number(),
  children: z.coerce.number(),
  departureDate: z.string().date(),
  destinationCode: z.string(),
  destinationName: z.string(),
  infants: z.coerce.number(),
  originCode: z.string(),
  returnDate: z.string().date(),
  page: z.coerce.number(),
  limit: z.coerce.number(),
  maxOfferPriceAud: z.coerce.number().optional(),
  minOfferPriceAud: z.coerce.number().optional(),
  propertyRatings: z
    .string()
    .optional()
    .transform((ratings, ctx) => {
      if (ratings === undefined) {
        return undefined;
      }

      const ratingsArray = ratings.split(',');
      const numberRatingsArray = ratingsArray.map((rating) => Number(rating));

      if (numberRatingsArray.some((rating) => Number.isNaN(rating))) {
        ctx.addIssue({
          code: z.ZodIssueCode.custom,
          message: 'Malformed data - rating was not a number',
        });
        return z.NEVER;
      }

      return numberRatingsArray;
    }),
  sort: z.enum(HOLIDAYS_PROPERTIES_SEARCH_SORT_OPTIONS).optional(),
  regionIds: z
    .string()
    .optional()
    .transform((regionIds, ctx) => {
      if (regionIds === undefined) {
        return undefined;
      }

      const regionIdsArray = regionIds.split(',');
      const numberRegionIdsArray = regionIdsArray.map((regionId) => Number(regionId));

      if (numberRegionIdsArray.some((regionId) => Number.isNaN(regionId))) {
        ctx.addIssue({
          code: z.ZodIssueCode.custom,
          message: 'Malformed data - regionId was not a number',
        });
        return z.NEVER;
      }

      return numberRegionIdsArray;
    }),
});

const imageSchema = z.object({
  urlLarge: z.string().url(),
  caption: z.string(),
});

export const propertyWithOfferSchema = z.object({
  tags: z.string().array().nullish(),
  id: z.string(),
  name: z.string(),
  tagline: z.string().nullish(),
  rating: z.number(),
  ratingType: z.enum(['AAA', 'SELF_RATED']),
  mainImage: imageSchema.nullish(),
  images: imageSchema.array().nullish(),
  procuredOffer: z
    .object({
      propertyName: z.string(),
      offerId: z.string(),
      terms: z.string(),
      propertyId: z.string(),
      packageOfferId: z.string(),
      inclusions: z
        .object({ icon: z.string(), title: z.string(), subtitle: z.string().nullish() })
        .array(),
      saleEndDate: z.string(),
      saleStartDate: z.string(),
      travelEndDate: z.string(),
      travelStartDate: z.string(),
      minNumberOfNights: z.number().nullable(),
      displayTravellerTypes: z.boolean(),
      extended: z.boolean().nullable(),
      description: z.string().nullable(),
    })
    .nullish(),
  offer: z.object({
    propertyId: z.string(),
    offerId: z.string(),
    charges: z.object({ total: z.string() }),
    roomTypeName: z.string(),
  }),
});

const metaSchema = z.object({
  totalPackageOffers: z.number(),
  minPrice: z.number().optional(),
  maxPrice: z.number().optional(),
});

export const getHolidaysPropertiesWithOfferResponseSchema = z.object({
  promoCodes: z.string().array(),
  propertiesWithOffer: z.object({}).passthrough().array(),
  meta: metaSchema,
});
