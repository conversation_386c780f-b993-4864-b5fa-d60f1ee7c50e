import { z } from 'zod';

import { HOLIDAYS_PROPERTIES_SEARCH_SORT_OPTIONS } from '../constants';

const numberToStringSchema = z.number().transform((number) => String(number));

export const holidaysPropertiesWithOfferHookSearchParamsSchema = z.object({
  adults: numberToStringSchema,
  children: numberToStringSchema,
  departureDate: z.string(),
  destinationCode: z.string(),
  destinationName: z.string(),
  infants: numberToStringSchema,
  originCode: z.string(),
  returnDate: z.string(),
  page: numberToStringSchema,
  limit: numberToStringSchema,
  maxOfferPriceAud: z.number().nullish(),
  minOfferPriceAud: z.number().nullish(),
  propertyRatings: z
    .number()
    .array()
    .nullish()
    .transform((ratings) => {
      if (!ratings) return undefined;

      return ratings.join(',');
    }),
  sort: z.enum(HOLIDAYS_PROPERTIES_SEARCH_SORT_OPTIONS).nullish(),
  regionIds: z
    .number()
    .array()
    .nullish()
    .transform((regionId) => {
      if (!regionId) return undefined;

      return regionId.join(',');
    }),
});
