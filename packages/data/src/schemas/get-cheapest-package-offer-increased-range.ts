import camelcaseKeys from 'camelcase-keys';
import { z } from 'zod';

import { imageSchema } from './image';
import { ratingSchema } from './rating';

// NOTE: These schemas don't include ALL data returned from the API, just the data we want.

// Meta
const metaSchema = z.object({
  pagination: z.object({ total_package_offers: z.number() }),
  price_range: z
    .object({ min_aud: z.number().optional(), max_aud: z.number().optional() })
    .optional(),
});

// Flights Options
const flightSegmentSchema = z.object({
  promotion_code: z.string().nullish(),
});

const journeySchema = z
  .object({
    flight_segments: flightSegmentSchema.array(),
  })
  .transform((journey) => {
    const promoCodes = journey.flight_segments.map((segment) => segment.promotion_code);
    return { promoCodes };
  });

const flightOptionsSchema = z
  .object({
    outbound_journeys: journeySchema.array(),
    inbound_journeys: journeySchema.array(),
  })
  .transform(({ inbound_journeys, outbound_journeys }) => {
    const inboundPromoCodes = inbound_journeys.flatMap((journey) => journey.promoCodes);
    const outboundPromoCodes = outbound_journeys.flatMap((journey) => journey.promoCodes);
    const allPromoCodes = [...inboundPromoCodes, ...outboundPromoCodes]
      .flat()
      .filter((value): value is string => !!value);
    const uniquePromoCodes = new Set(allPromoCodes);

    return {
      promoCodes: Array.from(uniquePromoCodes),
    };
  });

// Package Offers
const chargeSchema = z.object({ amount: z.string() }).transform(({ amount }) => amount);

const hotelOfferSchema = z.object({ id: z.string() });

const packageOfferIncreasedRangeSchema = z
  .object({
    property_id: z.string(),
    charges: z.object({ total: chargeSchema }),
    hotel_offer: hotelOfferSchema,
    room_type_name: z.string(),
    rating: z.number(),
    rating_type: ratingSchema,
    images: imageSchema.array().nullish(),
    main_image: imageSchema.nullish(),
    name: z.string(),
    room_type_id: z.string().optional(),
    longitude: z.number().optional(),
    latitude: z.number().optional(),
  })
  .transform(({ hotel_offer, ...rest }) => ({
    ...rest,
    offerId: hotel_offer.id,
  }));

export const getCheapestPackageOfferIncreasedRangeSchema = z
  .object({
    package_offers: packageOfferIncreasedRangeSchema.array(),
    flight_options: flightOptionsSchema,
    meta: metaSchema,
  })
  .transform((data) => camelcaseKeys(data, { deep: true }))
  .transform(({ flightOptions, packageOffers, meta }) => {
    return {
      packageOffers,
      promoCodes: flightOptions.promoCodes,
      meta: {
        totalPackageOffers: meta.pagination.totalPackageOffers,
        minPrice: meta.priceRange?.minAud,
        maxPrice: meta.priceRange?.maxAud,
      },
    };
  });

export type PavaPackageOfferIncreasedRange = z.infer<
  typeof getCheapestPackageOfferIncreasedRangeSchema
>['packageOffers'][number];

export type CheapestPackageOfferIncreasedRangeMeta = z.infer<
  typeof getCheapestPackageOfferIncreasedRangeSchema
>['meta'];

export type CheapestPackageOfferIncreasedRange = z.infer<
  typeof getCheapestPackageOfferIncreasedRangeSchema
>;
