'use client';

import { z } from 'zod';

import { featureFlagSchema, useFeatureFlag } from '../../hooks/useFeatureFlag';

const regionsVariableSchema = z.record(z.string(), z.union([z.number(), z.array(z.number())]));

const increasedRangeSchema = featureFlagSchema.extend({
  variables: z.object({
    regions: regionsVariableSchema,
  }),
  variationKey: z.enum(['enable_all_regions', 'enable_some_regions', 'off']),
});

export const useIncreasedRange = () => {
  const flag = useFeatureFlag('eja-increase-range');
  if (!flag) return;

  const { data, error } = increasedRangeSchema.safeParse(flag);
  if (error) {
    // TODO: log to sentry
    return;
  }

  return data;
};
