import 'server-only';

import { randomUUID } from 'crypto';
import { cookies } from 'next/headers';

interface GetUserIdFromCookieOptions {
  cookieName: string;
  shouldSetCookie: boolean;
}

export async function getUserIdFromCookie({
  cookieName,
  shouldSetCookie,
}: GetUserIdFromCookieOptions): Promise<string> {
  const cookieStore = await cookies();

  const cookieUserId = cookieStore.get(cookieName)?.value;

  const userId = !!cookieUserId && cookieUserId !== '' ? cookieUserId : randomUUID();

  if (shouldSetCookie) {
    cookieStore.set({
      name: cookieName,
      value: userId,
    });
  }

  return userId;
}
