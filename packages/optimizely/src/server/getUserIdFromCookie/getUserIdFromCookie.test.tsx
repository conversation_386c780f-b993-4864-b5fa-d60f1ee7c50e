/**
 * @jest-environment node
 * @jest-environment-options {"customExportConditions": ["react-server"]}
 */

import { cookies } from 'next/headers';

import { getUserIdFromCookie } from './getUserIdFromCookie';

jest.mock('next/headers');
jest.mock('crypto', () => ({
  randomUUID: jest.fn(() => 'generatedUuid'),
}));

const mockStore = [
  {
    key: 'someKey',
    value: 'abc',
  },
];

const get = jest.fn((key) => mockStore.find((item) => item.key === key));
const set = jest.fn();
const mockCookies = jest.mocked(cookies);

describe('getUserIdFromCookie', () => {
  beforeEach(() => {
    mockCookies.mockResolvedValue({ get, set } as unknown as Awaited<ReturnType<typeof cookies>>);
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  it('generates uuid when no cookie is found', async () => {
    const result = await getUserIdFromCookie({
      cookieName: 'doesNotExist',
      shouldSetCookie: false,
    });

    expect(result).toBe('generatedUuid');
  });

  it('returns value when cookie is found', async () => {
    const result = await getUserIdFromCookie({ cookieName: 'someKey', shouldSetCookie: false });

    expect(result).toBe('abc');
  });

  it('should not set the cookie if shouldSetCookie is false', async () => {
    await getUserIdFromCookie({ cookieName: 'someKey', shouldSetCookie: false });
    expect(set).not.toHaveBeenCalled();
  });

  it('should set the cookie again if shouldSetCookie is true', async () => {
    await getUserIdFromCookie({ cookieName: 'someKey', shouldSetCookie: true });
    expect(set).toHaveBeenCalledTimes(1);
    expect(set).toHaveBeenCalledWith({
      name: 'someKey',
      value: 'abc',
    });
  });
});
