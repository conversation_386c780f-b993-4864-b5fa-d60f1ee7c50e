import { render } from '@testing-library/react';

import { FEATURE_FLAG_USER_ID } from '../../constants';
import { FeatureFlagProvider } from '../FeatureFlagProvider';
import { getUserIdFromCookie } from '../getUserIdFromCookie';
import { CookieFeatureFlagProvider } from './CookieFeatureFlagProvider';

jest.mock('../getUserIdFromCookie', () => ({
  getUserIdFromCookie: jest.fn(() => 'abc'),
}));

jest.mock('../FeatureFlagProvider', () => ({
  FeatureFlagProvider: jest.fn(({ children }: { children: React.ReactNode }) => children),
}));

describe('CookieFeatureFlagProvider', () => {
  const mockChildren = <div>Child</div>;
  const mockAttributes = { foo: 'bar' };
  const cookieName = 'customUserIdCookieKey';
  const userId = 'abc';
  const sdkKey = 'publicSdkKey';

  const flags = ['example_feature_flag'];

  afterEach(() => {
    jest.clearAllMocks();
  });

  it('renders children inside CookieFeatureFlagProvider', async () => {
    const { findByText } = render(
      await CookieFeatureFlagProvider({ children: mockChildren, sdkKey }),
    );
    await expect(findByText('Child')).resolves.toBeInTheDocument();
  });

  it('renders FeatureFlagProvider with expected values', async () => {
    render(
      await CookieFeatureFlagProvider({
        children: mockChildren,
        attributes: mockAttributes,
        sdkKey,
        flags,
      }),
    );
    expect(FeatureFlagProvider).toHaveBeenCalledWith(
      {
        userId,
        flags,
        sdkKey,
        attributes: mockAttributes,
        children: mockChildren,
      },
      undefined,
    );
  });

  it('uses custom cookie name if provided', async () => {
    render(await CookieFeatureFlagProvider({ children: mockChildren, cookieName, sdkKey }));
    expect(getUserIdFromCookie).toHaveBeenCalledWith({
      cookieName,
      shouldSetCookie: false,
    });
  });

  it('uses default cookie name if one is not provided', async () => {
    render(await CookieFeatureFlagProvider({ children: mockChildren, sdkKey }));
    expect(getUserIdFromCookie).toHaveBeenCalledWith({
      cookieName: FEATURE_FLAG_USER_ID,
      shouldSetCookie: false,
    });
  });
});
