import type { UserAttributes } from '@optimizely/optimizely-sdk';
import type { ReactNode } from 'react';

import { FeatureFlagCookieSetter } from '../../client';
import { FEATURE_FLAG_USER_ID } from '../../constants';
import { FeatureFlagProvider } from '../FeatureFlagProvider';
import { getUserIdFromCookie } from '../getUserIdFromCookie';

export interface CookieFeatureFlagProviderProps {
  attributes?: UserAttributes;
  children?: ReactNode;
  cookieName?: string;
  flags?: string[];
  sdkKey: string;
}

/*
  This Provider is SSR and uses cookies.
  If you use custom cookie name, you can pass it in here.
  If you need to use localStorage to get/set your userId in some other way,
  you can follow this pattern and create another Provider, wrapping the BaseFeatureFlagProvider.
*/
export const CookieFeatureFlagProvider = async ({
  children,
  attributes,
  cookieName: cookieKey,
  flags,
  sdkKey,
}: CookieFeatureFlagProviderProps) => {
  const cookieName = cookieKey ?? FEATURE_FLAG_USER_ID;
  const userId = await getUserIdFromCookie({
    cookieName,
    shouldSetCookie: false,
  });

  return (
    <>
      <FeatureFlagProvider userId={userId} attributes={attributes} sdkKey={sdkKey} flags={flags}>
        {children}
      </FeatureFlagProvider>
      <FeatureFlagCookieSetter userId={userId} cookieName={cookieName} />
    </>
  );
};
