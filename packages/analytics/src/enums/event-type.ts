export enum EventType {
  /** User submits their payment information during checkout */
  ADD_PAYMENT_INFO = 'add_payment_info',

  /** User adds items to their shopping cart */
  ADD_TO_CART = 'add_to_cart',

  /** User begins checkout */
  BEGIN_CHECKOUT = 'begin_checkout',

  /** User encounters an error during checkout */
  CHECKOUT_ERROR = 'checkout_error',

  /** User adds a credit card */
  CREDIT_CARD_ADD = 'credit_card_add',

  /** User applies a credit card */
  CREDIT_CARD_APPLY = 'credit_card_apply',

  /** User clicks a cta link */
  CTA_CLICK = 'cta_click',

  /** User clicks a custom link */
  CUSTOM_CLICK = 'custom_click',

  /** User applies a filter option */
  FILTER_APPLY = 'filter_apply',

  /** User opens a filter dialog */
  FILTER_OPEN = 'filter_open',

  /** User selects a filter option */
  FILTER_SELECT = 'filter_select',

  /** User updates filter on your website or app */
  FILTER_UPDATE = 'filter_update',

  /** User clicks on a footer link */
  FOOTER_CLICK = 'footer_click',

  /** User submits a form successfully */
  FORM_COMPLETE = 'form_complete',

  /** User encounters an error on a form */
  FORM_ERROR = 'form_error',

  /** User successfully signs in */
  LOGIN = 'login',

  /** User successfully signs out */
  LOGOUT = 'logout',

  /** User clicks on map */
  MAP_LOCATION_CLICK = 'map_location_click',

  /** User clicks on a map button to open the map view for the search */
  MAP_OPEN = 'map_open',

  /** User clicks on a menu item (text/image/button in the navigation, header and menu). */
  MENU_CLICK = 'menu_click',

  /** User closes a modal */
  MODAL_CLOSE = 'modal_close',

  /** User is shown a modal */
  MODAL_SHOW = 'modal_show',

  /** optimizely make a decision */
  OPTIMIZELY_FEATURE_DECISION = 'optimizely_feature_decision',

  /** User views a page */
  PAGE_VIEW = 'page_view',

  /** User clicks on a pagination link */
  PAGINATION_CLICK = 'pagination_click',

  /** Promotion is applied */
  PROMOTION_CODE_APPLY = 'promotion_code_apply',

  /** User completes a purchase */
  PURCHASE = 'purchase',

  /** User submits a search and results in error */
  SEARCH_ERROR = 'search_error',

  /** User submits a search */
  SEARCH_EXIT_FIELD = 'search_exit_field',

  /** User submits a search */
  SEARCH_SUBMIT = 'search_submit',

  /** User selects an old search from the Home Page search */
  SEARCH_SUGGESTION_CLICK = 'search_suggestion_click',

  /** User visualizes the recent searches list */
  SEARCH_SUGGESTION_DISPLAY = 'search_suggestion_display',

  /** User selects an item from a list of items or offerings */
  SELECT_ITEM = 'select_item',

  /** User selects a promotion */
  SELECT_PROMOTION = 'select_promotion',

  /** User applies a sort option */
  SORT_APPLY = 'sort_apply',

  /** User opens a sort dialog */
  SORT_OPEN = 'sort_open',

  /** User selects a sort option */
  SORT_SELECT = 'sort_select',

  /** User clicks on a tab */
  TAB_CLICK = 'tab_click',

  /** User clicks on a tile */
  TILE_CLICK = 'tile_click',

  /** User views an item */
  VIEW_ITEM = 'view_item',

  /** User views a list of items or offerings */
  VIEW_ITEM_LIST = 'view_item_list',

  /** User views a promotion on your website or app */
  VIEW_PROMOTION = 'view_promotion',

  /** User searches on your website or app */
  VIEW_SEARCH_RESULTS = 'view_search_results',

  // /** User submits their shipping information during checkout */
  // ADD_SHIPPING_INFO = 'add_shipping_info',

  // /** User adds items to their wishlist */
  // ADD_TO_WISHLIST = 'add_to_wishlist',

  // /** User receives a refund */
  // REFUND = 'refund',

  // /** User removes items from their shopping cart */
  // REMOVE_FROM_CART = 'remove_from_cart',

  // /** User views their shopping cart */
  // VIEW_CART = 'view_cart',
}
