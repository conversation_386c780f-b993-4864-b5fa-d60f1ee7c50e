export interface CreateHotelsOptimizelyFeatureDecisionEventPayloadOptions {
  enabled: string;
  // The string is a combination of three keys: flagKey, variationKey, and ruleKey, with each key in kebab-case and separated by underscores.
  // Multiple experiments are separated by commas.
  fxp_variant_string: string;
}

export interface OptimizelyObject {
  enabled: string;
  fxp_variant_string: string;
}

export interface CreateHotelsOptimizelyFeatureDecisionEventPayloadReturn {
  optimizely: OptimizelyObject;
}

const FXP_VARIANT_REGEX =
  /^([a-z0-9-]+_[a-z0-9-]+_[a-z0-9-]+)(,[a-z0-9-]+_[a-z0-9-]+_[a-z0-9-]+)*$/;

export const createHotelsOptimizelyFeatureDecisionEventPayload = ({
  enabled,
  fxp_variant_string,
}: CreateHotelsOptimizelyFeatureDecisionEventPayloadOptions): CreateHotelsOptimizelyFeatureDecisionEventPayloadReturn => {
  if (!FXP_VARIANT_REGEX.test(fxp_variant_string)) {
    throw new Error(
      `fxp_variant_string must be in the format 'flagKey_variationKey_ruleKey' (kebab-case, separated by underscores, multiple experiments separated by commas). Received: ${fxp_variant_string}`,
    );
  }
  const enabledString = String(enabled);

  return {
    optimizely: {
      fxp_variant_string,
      enabled: enabledString,
    },
  };
};
