import { createHotelsOptimizelyFeatureDecisionEventPayload } from './createHotelsOptimizelyFeatureDecisionEventPayload';

describe('createHotelsOptimizelyFeatureDecisionEventPayload', () => {
  it('throws an error for invalid fxp_variant_string format', () => {
    expect(() =>
      createHotelsOptimizelyFeatureDecisionEventPayload({
        fxp_variant_string: 'invalidFormatString',
        enabled: 'true',
      }),
    ).toThrow(/fxp_variant_string must be in the format/);
  });

  it('creates a valid event when Optimizely makes a decision', () => {
    const result = createHotelsOptimizelyFeatureDecisionEventPayload({
      fxp_variant_string: 'qantas-hotels-available-rooms-message_on_default-rollout-4',
      enabled: 'true',
    });

    expect(result).toStrictEqual({
      optimizely: {
        fxp_variant_string: 'qantas-hotels-available-rooms-message_on_default-rollout-4',
        enabled: 'true',
      },
    });
  });
});
