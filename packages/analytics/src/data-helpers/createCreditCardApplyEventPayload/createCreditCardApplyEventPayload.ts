import { ActionType, ComponentType } from '../../enums';

export interface CreateCreditCardApplyEventPayloadOptions {
  itemName: string;
  itemType: string;
  paymentType: string;
  provider: string;
  savedCard: boolean;
  url: string;
}

export interface CreateCreditCardApplyEventPayloadReturn {
  event_data: {
    action: ActionType.APPLY;
    component_type: ComponentType.ADYEN_PAYMENT_WIDGET;
    item_name: string;
    item_type: string;
    payment_type: string;
    provider: string;
    saved_card: boolean;
    url: string;
  };
}

export const createCreditCardApplyEventPayload = ({
  itemName,
  itemType,
  paymentType,
  provider,
  savedCard,
  url,
}: CreateCreditCardApplyEventPayloadOptions): CreateCreditCardApplyEventPayloadReturn => {
  return {
    event_data: {
      action: ActionType.APPLY,
      component_type: ComponentType.ADYEN_PAYMENT_WIDGET,
      item_name: itemName,
      item_type: itemType,
      payment_type: paymentType,
      provider,
      saved_card: savedCard,
      url,
    },
  };
};
