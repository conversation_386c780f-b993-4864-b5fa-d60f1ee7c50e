import { ActionType, ComponentType } from '../../enums';
import { createCreditCardApplyEventPayload } from './createCreditCardApplyEventPayload';

describe('createCreditCardApplyEventPayload', () => {
  it('creates a valid credit_card_apply event payload', () => {
    const result = createCreditCardApplyEventPayload({
      itemName: 'Adyen',
      itemType: 'Payment Widget',
      provider: 'amex',
      paymentType: 'credit card',
      savedCard: true,
      url: 'https://www.qantas.com',
    });

    expect(result).toStrictEqual({
      event_data: {
        action: ActionType.APPLY,
        component_type: ComponentType.ADYEN_PAYMENT_WIDGET,
        item_name: 'Adyen',
        item_type: 'Payment Widget',
        payment_type: 'credit card',
        provider: 'amex',
        saved_card: true,
        url: 'https://www.qantas.com',
      },
    });
  });
});
