import { ActionType, ComponentType } from '../../enums';
import { createCreditCardAddEventPayload } from './createCreditCardAddEventPayload';

describe('createCreditCardAddEventPayload', () => {
  it('creates a valid credit_card_add event payload', () => {
    const result = createCreditCardAddEventPayload({
      itemName: 'Adyen',
      itemType: 'Payment Widget',
      provider: 'amex',
      paymentType: 'credit card',
      savedCard: false,
      url: 'https://www.qantas.com',
    });

    expect(result).toStrictEqual({
      event_data: {
        action: ActionType.ADD,
        component_type: ComponentType.ADYEN_PAYMENT_WIDGET,
        item_name: 'Adyen',
        item_type: 'Payment Widget',
        payment_type: 'credit card',
        provider: 'amex',
        saved_card: false,
        url: 'https://www.qantas.com',
      },
    });
  });
});
