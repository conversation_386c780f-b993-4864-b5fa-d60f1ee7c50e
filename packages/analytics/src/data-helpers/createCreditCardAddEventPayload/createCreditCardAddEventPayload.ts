import { ActionType, ComponentType } from '../../enums';

export interface CreateCreditCardAddEventPayloadOptions {
  itemName: string;
  itemType: string;
  paymentType: string;
  provider: string;
  savedCard: boolean;
  url: string;
}

export interface CreateCreditCardAddEventPayloadReturn {
  event_data: {
    action: ActionType.ADD;
    component_type: ComponentType.ADYEN_PAYMENT_WIDGET;
    item_name: string;
    item_type: string;
    payment_type: string;
    provider: string;
    saved_card: boolean;
    url: string;
  };
}

export const createCreditCardAddEventPayload = ({
  itemName,
  itemType,
  paymentType,
  provider,
  savedCard,
  url,
}: CreateCreditCardAddEventPayloadOptions): CreateCreditCardAddEventPayloadReturn => {
  return {
    event_data: {
      action: ActionType.ADD,
      component_type: ComponentType.ADYEN_PAYMENT_WIDGET,
      item_name: itemName,
      item_type: itemType,
      payment_type: paymentType,
      provider,
      saved_card: savedCard,
      url,
    },
  };
};
