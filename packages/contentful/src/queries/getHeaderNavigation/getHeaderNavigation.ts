import type { Tenant } from '@qantasexperiences/tenants';
import { getTenantSlug } from '@qantasexperiences/tenants';

import { fetchGraphQL } from '../fetchGraphQL';
import { getHeaderNavigationSchema } from './getHeaderNavigation.schema';

async function getHeader(tenant: Tenant, preview: boolean) {
  const slug = getTenantSlug(tenant);

  // eslint-disable-next-line @typescript-eslint/no-unsafe-assignment
  const entries = await fetchGraphQL(
    `query {
      headerCollection(where: { app: { slug: "${slug}" } }, limit: 1) {
        items {
          navItemsCollection(limit: 7) {
            items {
              ... on ExternalLink { ...externalLink }
              ... on InternalLink { ...internalLink }
              ... on NavCollection {
                ...navCollection
                items: itemsCollection(limit: 18) {
                  items {
                    ... on ExternalLink { ...externalLink }
                    ... on InternalLink { ...internalLink }
                    ... on NavCollection {
                      ...navCollection
                      items: itemsCollection(where: { sys: { id: "" } }, limit: 1) {
                        items {
                          __typename
                        }
                      }
                    }
                  }
                } 
              }
            }
          }
          megaNavItemsCollection: navItemsCollection(limit: 1, where: { contentfulMetadata:{ tags: { id_contains_all: "mega" } } }) {
            items {
              ... on ExternalLink { ...externalLink }
              ... on InternalLink { ...internalLink }
              ... on NavCollection {
                ...navCollection
                items: itemsCollection(limit: 8) {
                  items {
                    ... on ExternalLink { ...externalLink }
                    ... on InternalLink { ...internalLink }
                    ... on NavCollection {
                      ...navCollection
                      items: itemsCollection(limit: 24) {
                        items {
                          ... on ExternalLink { ...externalLink }
                          ... on InternalLink { ...internalLink }
                          ... on NavCollection {
                            ...navCollection
                            items: itemsCollection(limit: 14) {
                              items {
                                ... on ExternalLink { ...externalLink }
                                ... on InternalLink { ...internalLink }
                              }
                            }
                          }
                        }
                      }
                    }
                  }
                } 
              }
            }
          }
        }
      }
    }
    
    fragment internalLink on InternalLink { sys { id }, title, page { slug, app { slug } } }
    fragment externalLink on ExternalLink { sys { id }, title, url }
    fragment navCollection on NavCollection { sys { id }, title, titleLink { ... on ExternalLink { ...externalLink }, ... on InternalLink { ...internalLink } } }`,
    preview,
  );

  const { success, data } = getHeaderNavigationSchema.safeParse(
    // eslint-disable-next-line @typescript-eslint/no-unsafe-member-access
    entries?.data?.headerCollection?.items?.[0],
  );

  if (!success) return null;

  return data;
}

export { getHeader };
