import type { Brand } from '@qantasexperiences/tenants';
import { logger } from '@qantasexperiences/logger';
import { safePartialParseArray } from '@qantasexperiences/utils/zod';

import { fetchGraphQL } from '../fetchGraphQL';
import { destinationSchema, getHolidaysDestinationsSchema } from './getHolidaysDestinations.schema';

interface GetHolidaysDestinationsParams {
  brand: Brand;
}

export const getHolidaysDestinations = async (
  { brand }: GetHolidaysDestinationsParams,
  preview = false,
) => {
  try {
    const query = `
  query HolidaysDestinationDropdown {
  destinationCollection(
    where: { 
      AND: [
        { airport_exists: true },
        { brands: {id_in: ["${brand}"]} }
      ] 
    }
  ) {
    items {
      name
      slug
      thumbnail: ${brand}Thumbnail {
        url
      }
      searchTags
      airport {
        name
        code
        alias: ${brand}Alias
        originAirports: ${brand}OriginAirportsCollection(limit: 80) {
          items {
            code
          }
        }
      }
      stateOrCountry {
        __typename
        ... on State {
          name
          country {
            name
          }
        }
        ... on Country {
          name
        }
      }
      searchTags
      regionIds: regionIdsCollection(limit: 6) {
        items {
          regionId
        }
      }
    }
  }
} 
`;

    const { data, success, error } = getHolidaysDestinationsSchema.safeParse(
      await fetchGraphQL(query, preview),
    );

    if (!success) {
      logger.error(error, `${brand} getHolidaysDestinations Contentful query`);
      return null;
    }

    const { validItems: destinations, errors } = safePartialParseArray(data, destinationSchema);

    if (errors) {
      errors.forEach((_error) => {
        logger.warn(_error.formatForLogging(), `${brand} getHolidaysDestinations Contentful query`);
      });
    }

    return destinations;
  } catch (e) {
    logger.error(e, `${brand} getHolidaysDestinations Contentful query`);
    return null;
  }
};
