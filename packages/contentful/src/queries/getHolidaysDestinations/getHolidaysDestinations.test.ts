import { logger } from '@qantasexperiences/logger';

import { fetchGraphQL } from '../fetchGraphQL';
import { getHolidaysDestinations } from './getHolidaysDestinations';

jest.mock('../fetchGraphQL', () => ({
  fetchGraphQL: jest.fn(),
}));
jest.mock('@qantasexperiences/logger', () => ({
  logger: { error: jest.fn(), warn: jest.fn() },
}));

const createData = (items: unknown[]) =>
  Promise.resolve({
    data: {
      destinationCollection: {
        items,
      },
    },
  });

const MELBOURNE = {
  name: 'Melbourne',
  slug: 'melbourne',
  searchTags: null,
  thumbnail: {
    url: 'my-thumbnail-url',
  },
  airport: {
    name: 'Melbourne (Tullamarine)',
    code: 'MEL',
    alias: 'Melbourne',
    originAirports: {
      items: [
        {
          code: 'SYD',
        },
        {
          code: 'BNE',
        },
      ],
    },
  },
  stateOrCountry: {
    __typename: 'State',
    name: 'Victoria',
    country: {
      name: 'Australia',
    },
  },
  regionIds: {
    items: [
      {
        regionId: 100,
      },
      {
        regionId: 200,
      },
    ],
  },
};

const SYDNEY = {
  name: 'Sydney',
  slug: 'sydney',
  searchTags: null,
  thumbnail: {
    url: 'my-thumbnail-url',
  },
  stateOrCountry: {
    __typename: 'State',
    name: 'Queensland',
    country: {
      name: 'Australia',
    },
  },
  airport: {
    name: 'Sydney Kingsford Smith',
    code: 'SYD',
    alias: 'Sydney',
    originAirports: {
      items: [
        {
          code: 'MEL',
        },
      ],
    },
  },
  regionIds: {
    items: [
      {
        regionId: 300,
      },
    ],
  },
};

const TOKYO = {
  name: 'Tokyo',
  slug: 'tokyo',
  searchTags: null,
  thumbnail: {
    url: 'my-thumbnail-url',
  },
  airport: {
    name: 'Haneda',
    code: 'HND',
    alias: null,
    originAirports: {
      items: [{ code: 'BNE' }],
    },
  },
  stateOrCountry: {
    __typename: 'Country',
    name: 'Japan',
  },
};

const SYDNEY_NO_AIRPORT_INVALID = {
  ...SYDNEY,
  airport: null,
};

const SYDNEY_NO_ORIGINS_INVALID = {
  ...SYDNEY,
  airport: {
    name: 'Sydney Kingsford Smith',
    code: 'SYD',
    alias: 'Sydney',
    originAirports: {
      items: [],
    },
  },
};

const SYDNEY_NO_NAME_INVALID = {
  ...SYDNEY,
  name: null,
};

describe('getHolidaysDestination', () => {
  afterEach(() => {
    jest.resetAllMocks();
  });

  it('returns correctly transformed data', async () => {
    jest.mocked(fetchGraphQL).mockReturnValue(createData([MELBOURNE, SYDNEY, TOKYO]));

    await expect(getHolidaysDestinations({ brand: 'jetstar' })).resolves.toStrictEqual([
      {
        name: 'Melbourne',
        slug: 'melbourne',
        searchTags: null,
        thumbnailUrl: 'my-thumbnail-url',
        state: 'Victoria',
        country: 'Australia',
        airport: {
          name: 'Melbourne (Tullamarine)',
          code: 'MEL',
          alias: 'Melbourne',
          displayName: 'Melbourne - MEL',
        },
        originCodes: ['SYD', 'BNE'],
        regionIds: [100, 200],
      },
      {
        name: 'Sydney',
        slug: 'sydney',
        searchTags: null,
        thumbnailUrl: 'my-thumbnail-url',
        state: 'Queensland',
        country: 'Australia',
        airport: {
          name: 'Sydney Kingsford Smith',
          code: 'SYD',
          alias: 'Sydney',
          displayName: 'Sydney - SYD',
        },
        originCodes: ['MEL'],
        regionIds: [300],
      },
      {
        name: 'Tokyo',
        slug: 'tokyo',
        searchTags: null,
        thumbnailUrl: 'my-thumbnail-url',
        state: undefined,
        country: 'Japan',
        airport: {
          name: 'Haneda',
          code: 'HND',
          alias: null,
          displayName: 'Haneda - HND',
        },
        originCodes: ['BNE'],
        regionIds: undefined,
      },
    ]);
  });

  it('returns null and logs error if basic response shape is incorrect', async () => {
    jest.mocked(fetchGraphQL).mockResolvedValue({ data: { wrongKey: [] } });

    await expect(getHolidaysDestinations({ brand: 'jetstar' })).resolves.toBeNull();
    expect(logger.error).toHaveBeenNthCalledWith(
      1,
      expect.objectContaining({}),
      'jetstar getHolidaysDestinations Contentful query',
    );
  });

  it('filters out malformed destinations', async () => {
    jest
      .mocked(fetchGraphQL)
      .mockReturnValue(
        createData([
          SYDNEY,
          null,
          SYDNEY_NO_AIRPORT_INVALID,
          SYDNEY_NO_ORIGINS_INVALID,
          SYDNEY_NO_NAME_INVALID,
        ]),
      );

    await expect(getHolidaysDestinations({ brand: 'jetstar' })).resolves.toStrictEqual([
      {
        name: 'Sydney',
        slug: 'sydney',
        searchTags: null,
        thumbnailUrl: 'my-thumbnail-url',
        state: 'Queensland',
        country: 'Australia',
        airport: {
          name: 'Sydney Kingsford Smith',
          code: 'SYD',
          alias: 'Sydney',
          displayName: 'Sydney - SYD',
        },
        originCodes: ['MEL'],
        regionIds: [300],
      },
    ]);
  });

  it('logs EnhancedZodErrors as warnings for each malformed destination', async () => {
    jest
      .mocked(fetchGraphQL)
      .mockReturnValue(
        createData([
          SYDNEY,
          null,
          SYDNEY_NO_AIRPORT_INVALID,
          SYDNEY_NO_ORIGINS_INVALID,
          SYDNEY_NO_NAME_INVALID,
        ]),
      );

    await getHolidaysDestinations({ brand: 'jetstar' });

    expect(logger.warn).toHaveBeenCalledTimes(4);
    expect(logger.warn).toHaveBeenNthCalledWith(
      1,
      expect.objectContaining({
        entityWithError: null,
        // eslint-disable-next-line @typescript-eslint/no-unsafe-assignment
        zodError: expect.objectContaining({
          issues: [expect.objectContaining({ path: [] })],
        }),
      }),
      'jetstar getHolidaysDestinations Contentful query',
    );
    expect(logger.warn).toHaveBeenNthCalledWith(
      2,
      expect.objectContaining({
        entityWithError: SYDNEY_NO_AIRPORT_INVALID,
        // eslint-disable-next-line @typescript-eslint/no-unsafe-assignment
        zodError: expect.objectContaining({
          issues: [expect.objectContaining({ path: ['airport'] })],
        }),
      }),
      'jetstar getHolidaysDestinations Contentful query',
    );
    expect(logger.warn).toHaveBeenNthCalledWith(
      3,
      expect.objectContaining({
        entityWithError: SYDNEY_NO_ORIGINS_INVALID,
        // eslint-disable-next-line @typescript-eslint/no-unsafe-assignment
        zodError: expect.objectContaining({
          issues: [expect.objectContaining({ path: ['airport', 'originAirports', 'items'] })],
        }),
      }),
      'jetstar getHolidaysDestinations Contentful query',
    );
    expect(logger.warn).toHaveBeenNthCalledWith(
      4,
      expect.objectContaining({
        entityWithError: SYDNEY_NO_NAME_INVALID,
        // eslint-disable-next-line @typescript-eslint/no-unsafe-assignment
        zodError: expect.objectContaining({
          issues: [expect.objectContaining({ path: ['name'] })],
        }),
      }),
      'jetstar getHolidaysDestinations Contentful query',
    );
  });

  it('returns null and logs message if an error occurs', async () => {
    jest.mocked(fetchGraphQL).mockImplementation(() => {
      throw new Error('test error');
    });

    await expect(getHolidaysDestinations({ brand: 'qantas' })).resolves.toBeNull();
    expect(logger.error).toHaveBeenNthCalledWith(
      1,
      expect.objectContaining({ message: 'test error' }),
      'qantas getHolidaysDestinations Contentful query',
    );
  });
});
