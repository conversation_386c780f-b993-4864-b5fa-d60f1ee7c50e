import { z } from 'zod';

import { baseAirportSchema } from '../../schemas';
import {
  country as defaultCountrySchema,
  state as defaultStateSchema,
  destination,
} from '../../schemas/place';

const airportSchema = baseAirportSchema
  .required({
    name: true,
    code: true,
    alias: true,
  })
  .extend({
    originAirports: z.object({
      items: z.object({ code: z.string() }).array().nonempty(),
    }),
  });

// Same as default country schema but optional slug and mandatory __typename
const countrySchema = defaultCountrySchema.partial({ slug: true }).required({ __typename: true });

// Same as default state schema but optional slug and mandatory __typename + country with optional slug
const stateSchema = defaultStateSchema
  .partial({ slug: true })
  .required({ __typename: true })
  .extend({
    country: countrySchema.partial({ __typename: true }),
  });

const isState = (
  stateOrCountry: z.infer<typeof countrySchema> | z.infer<typeof stateSchema>,
): stateOrCountry is z.infer<typeof stateSchema> => 'country' in stateOrCountry;

/*
Same as default destination schema with changes:
    - airport mandatory
    - metadata remove
    - stateOrCountry uses optional schemas defined above
    - add airport displayName
    - extracts airport's origin airports to top-level originCodes
    - extracts regionIds items to top-level regionIds
 */
export const destinationSchema = destination
  .required({ airport: true })
  .omit({ metadata: true })
  .extend({
    airport: airportSchema,
    stateOrCountry: z.discriminatedUnion('__typename', [stateSchema, countrySchema]),
  })
  // Transform originAirports on airport to top-level originCodes
  .transform(
    ({
      airport: { originAirports, ...restAirport },
      thumbnail,
      stateOrCountry,
      regionIds,
      ...rest
    }) => {
      const { state, country } = isState(stateOrCountry)
        ? { state: stateOrCountry.name, country: stateOrCountry.country.name }
        : { state: undefined, country: stateOrCountry.name };

      return {
        ...rest,
        airport: {
          ...restAirport,
          displayName: [restAirport.alias ?? restAirport.name, restAirport.code].join(' - '),
        },
        originCodes: originAirports.items.map(({ code }) => code),
        thumbnailUrl: thumbnail?.url,
        state,
        country,
        regionIds: regionIds?.items.map(({ regionId }) => regionId),
      };
    },
  );

// Verifies flattens the general shape returned from contentful (data > destinationCollection > items) without specifying
// what the items array contains.
export const getHolidaysDestinationsSchema = z
  .object({
    data: z.object({
      destinationCollection: z.object({
        items: z.object({}).passthrough().nullable().array(),
      }),
    }),
  })
  .transform(({ data }) => {
    return data.destinationCollection.items;
  });
