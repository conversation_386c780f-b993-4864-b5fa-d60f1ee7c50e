import { render, screen } from '@testing-library/react';
import userEvent from '@testing-library/user-event';

import { sendCtaClickEvent } from '../../../analytics/sendCtaClickEvent';
import { SearchMoreHotelsButton } from './SearchMoreHotelsButton';

jest.mock('../../../analytics/sendCtaClickEvent', () => ({
  sendCtaClickEvent: jest.fn(),
}));

describe('SearchMoreHotelsButton', () => {
  afterEach(() => {
    jest.clearAllMocks();
  });

  afterAll(() => {
    jest.restoreAllMocks();
  });

  const user = userEvent.setup();

  const cases = [
    {
      name: 'removes all utm_* params (lowercase)',
      input:
        'https://www.qantas.com/hotels/search/list?utm_source=merch&utm_medium=&utm_campaign=direct&featuredPropertyId=674278',
      expected: 'https://www.qantas.com/hotels/search/list?featuredPropertyId=674278',
    },
    {
      name: 'removes all utm_* params (uppercase)',
      input:
        'https://www.qantas.com/hotels/search/list?UTM_SOURCE=merch&UTM_medium=&UTM_Campaign=direct&featuredPropertyId=674278',
      expected: 'https://www.qantas.com/hotels/search/list?featuredPropertyId=674278',
    },
    {
      name: 'removes mixed-case utm params',
      input:
        'https://www.qantas.com/hotels/search/list?UtM_source=merch&uTm_medium=&utm_Campaign=direct&featuredPropertyId=674278',
      expected: 'https://www.qantas.com/hotels/search/list?featuredPropertyId=674278',
    },
    {
      name: 'only utm params, no trailing ?',
      input: 'https://www.qantas.com/hotels/search/list?utm_source=merch&utm_medium=test',
      expected: 'https://www.qantas.com/hotels/search/list',
    },
    {
      name: 'no utm params',
      input: 'https://www.qantas.com/hotels/search/list?featuredPropertyId=123&location=Sydney',
      expected: 'https://www.qantas.com/hotels/search/list?featuredPropertyId=123&location=Sydney',
    },
  ];

  it.each(cases)('$name and calls sendCtaClickEvent on click', async ({ input, expected }) => {
    // eslint-disable-next-line @typescript-eslint/no-empty-function
    const errorSpy = jest.spyOn(console, 'error').mockImplementation(() => {});
    render(<SearchMoreHotelsButton url={input} />);

    await user.click(screen.getByRole('link', { name: 'Search more hotels' }));
    expect(sendCtaClickEvent).toHaveBeenCalledWith({
      itemText: 'Search more hotels',
      itemType: 'button',
      url: expected,
    });
    expect(errorSpy).toHaveBeenCalled(); // navigation not implemented in jsdom
    errorSpy.mockRestore();
  });

  it('should render the button with correct text and URL', () => {
    const url = 'https://example.com/search';
    render(<SearchMoreHotelsButton url={url} />);

    expect(screen.getByText('Search more hotels')).toHaveAttribute('href', url);
  });

  it('should render URL without utm tags', () => {
    const url = 'https://example.com/search?utm_source=test&utm_medium=test&location=Sydney';
    render(<SearchMoreHotelsButton url={url} />);

    expect(screen.getByText('Search more hotels')).toHaveAttribute(
      'href',
      'https://example.com/search?location=Sydney',
    );
  });
});
