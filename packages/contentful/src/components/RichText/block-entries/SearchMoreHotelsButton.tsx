'use client';

import { removeUtmParams } from '@qantasexperiences/utils/formatting';

import { sendCtaClickEvent } from '../../../analytics/sendCtaClickEvent';

interface SearchMoreHotelsButtonProps {
  url: string;
}

export function SearchMoreHotelsButton({ url }: SearchMoreHotelsButtonProps) {
  const cleanUrl = removeUtmParams(url);

  const handleOnClick = () => {
    sendCtaClickEvent({
      itemText: 'Search more hotels',
      itemType: 'button',
      url: cleanUrl,
    });
  };

  return (
    <a
      href={cleanUrl}
      className="not-prose mx-auto block w-max rounded border-2 border-red-600 px-6 py-3 text-lg font-semibold tracking-widest text-red-600 uppercase hover:bg-red-600 hover:text-white"
      onClick={handleOnClick}
    >
      Search more hotels
    </a>
  );
}
