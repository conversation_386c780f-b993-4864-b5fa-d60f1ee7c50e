import { render, screen } from '@testing-library/react';

import { getDestinationProperties } from '@qantasexperiences/utils/api';

import { MerchandisingProperties } from './MerchandisingProperties';
import { MerchandisingPropertyTabs } from './MerchandisingPropertyTabs';

jest.mock('@qantasexperiences/utils/api');
jest.mock('./MerchandisingPropertyTabs');

const mockedGetDestinationProperties = jest.mocked(getDestinationProperties);
const MockedMerchandisingPropertyTabs = jest.mocked(MerchandisingPropertyTabs);

const mockDestinationData = {
  properties: [
    {
      id: 'test-id',
      name: 'Test Property',
      address: {
        countryCode: 'AU',
        streetAddress: ['123 Test St'],
        country: 'Australia',
        suburb: 'Test Suburb',
      },
      mainImage: {
        urlMedium: 'https://example.com/medium.jpg',
        urlLarge: 'https://example.com/large.jpg',
        urlSmall: 'https://example.com/small.jpg',
        urlHighres: 'https://example.com/highres.jpg',
        urlOriginal: 'https://example.com/original.jpg',
        caption: 'Test Hotel Image',
        id: 'img-1',
      },
      offer: {
        charges: {
          total: { amount: 200 },
          discount: { amount: 0 },
          currency: 'AUD',
        },
        inclusions: ['Free WiFi'],
        promotion: null,
        cancellation: [
          {
            startTime: '2024-01-01',
            endTime: '2024-12-31',
            currency: 'AUD',
          },
        ],
        roomType: 'Standard Room',
        pointsEarned: { qffPoints: 1000 },
      },
      propertyAvailabilityUrl: '/book',
      pointsEarned: { qffPoints: 1000 },
      rating: { value: 4, type: 'AAA' as const },
      charges: {
        total: { amount: 200 },
        discount: { amount: 0 },
        currency: 'AUD',
      },
      availabilitySearchUrl: 'https://example.com/search',
      tripadvisor: {
        averageRating: 4.5,
        reviewCount: 100,
      },
    },
  ],
  totalProperties: 1,
  subRegions: [],
  regionAvailabilitySearchUrl: 'https://example.com/search?utm_source=test',
};

describe('MerchandisingProperties', () => {
  beforeEach(() => {
    MockedMerchandisingPropertyTabs.mockImplementation(
      (props: React.ComponentProps<typeof MerchandisingPropertyTabs>) => (
        <div data-testid="property-tabs">
          {props.regions.map((region) => (
            <div key={region.regionId}>
              <p>
                {region.regionName} - {region.properties.length} properties
              </p>
            </div>
          ))}
        </div>
      ),
    );
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  it('renders nothing if regionId is empty', async () => {
    const result = await MerchandisingProperties({});

    const { container } = render(result);

    expect(mockedGetDestinationProperties).not.toHaveBeenCalled();
    expect(container).toBeEmptyDOMElement();
  });

  it('renders nothing if no properties are returned', async () => {
    mockedGetDestinationProperties.mockResolvedValue({
      properties: [],
      totalProperties: 0,
      subRegions: [],
      regionAvailabilitySearchUrl: '',
    });

    const result = await MerchandisingProperties({
      regionId: 'test-region',
    });
    const { container } = render(result);

    expect(mockedGetDestinationProperties).toHaveBeenCalledWith('test-region');
    expect(container).toBeEmptyDOMElement();
  });

  it('renders properties with title and search information', async () => {
    mockedGetDestinationProperties.mockResolvedValue(mockDestinationData);

    const result = await MerchandisingProperties({
      regionId: 'test-region',
      searchTerm: 'Test Location',
      title: 'Test Title',
    });
    render(result);

    expect(mockedGetDestinationProperties).toHaveBeenCalledWith('test-region');
    expect(screen.getByText('Test Title')).toBeInTheDocument();
    expect(screen.getByText('Popular - 1 properties')).toBeInTheDocument();
    expect(screen.getByText('Search 1 hotels in Test Location')).toBeInTheDocument();
    expect(screen.getByRole('link', { name: 'Search more hotels' })).toHaveAttribute(
      'href',
      'https://example.com/search',
    );
  });

  it('renders properties without title and search information', async () => {
    mockedGetDestinationProperties.mockResolvedValue(mockDestinationData);

    const result = await MerchandisingProperties({
      regionId: 'test-region',
    });
    render(result);

    expect(mockedGetDestinationProperties).toHaveBeenCalledWith('test-region');
    expect(screen.queryByText('Test Title')).not.toBeInTheDocument();
    expect(screen.getByText('Popular - 1 properties')).toBeInTheDocument();
    expect(screen.queryByText('Search 1 hotels in Test Location')).not.toBeInTheDocument();
    expect(screen.getByRole('link', { name: 'Search more hotels' })).toBeInTheDocument();
  });

  it('renders primary CTA without UTM tags', async () => {
    mockedGetDestinationProperties.mockResolvedValue(mockDestinationData);

    const result = await MerchandisingProperties({
      regionId: 'test-region',
      searchTerm: 'Test Location',
      title: 'Test Title',
    });
    render(result);

    expect(screen.getByRole('link', { name: 'Search hotels' })).toHaveAttribute(
      'href',
      'https://example.com/search',
    );
  });
});
