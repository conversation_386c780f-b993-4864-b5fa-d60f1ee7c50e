import { getDestinationProperties } from '@qantasexperiences/utils/api';
import { removeUtmParams } from '@qantasexperiences/utils/formatting';

import { MerchandisingPropertyTabs } from './MerchandisingPropertyTabs';
import { SearchHotelsButton } from './SearchHotelsButton';
import { SearchMoreHotelsButton } from './SearchMoreHotelsButton';

interface MerchandisingPropertiesProps {
  regionId?: string | null;
  searchTerm?: string;
  title?: string | null;
}

export const MerchandisingProperties = async ({
  title,
  regionId,
  searchTerm,
}: MerchandisingPropertiesProps) => {
  if (!regionId) return null;

  const data = await getDestinationProperties(regionId);

  if (!data || data.properties.length === 0) return null;

  return (
    <>
      <div className="mt-14 text-center">
        <SearchHotelsButton url={removeUtmParams(data.regionAvailabilitySearchUrl)} />
      </div>
      <div className="rt-contained" data-ref="qec-merchandising-properties">
        {title && <h2>{title}</h2>}
        <MerchandisingPropertyTabs
          regions={[
            {
              regionId,
              regionName: 'Popular',
              properties: data.properties,
              totalProperties: data.totalProperties,
              regionAvailabilitySearchUrl: data.regionAvailabilitySearchUrl,
            },
            ...data.subRegions,
          ]}
        />
        <p className="text-center text-neutral-600">
          Search {data.totalProperties} hotels in {searchTerm}
        </p>
        <div className="mt-4 flex flex-col items-center">
          <SearchMoreHotelsButton url={data.regionAvailabilitySearchUrl} />
        </div>
      </div>
    </>
  );
};
