// TODO: cleanup place schemas
import { z } from 'zod';

import { airportSchema } from './airport';

export const placeMetadata = z.object({
  avaRegionId: z.string().nullish(),
  avaSearchTerm: z.string(),
});

export const regionId = z.object({
  regionId: z.number(),
});

const basePlace = z.object({
  name: z.string(),
  slug: z.string(),
});

export const country = basePlace.extend({
  __typename: z.literal('Country'),
});

export const state = basePlace.extend({
  __typename: z.literal('State'),
  country,
});

export const city = basePlace.extend({
  __typename: z.literal('City'),
  state: state.nullable(),
  metadata: placeMetadata,
});

export const destination = basePlace.extend({
  metadata: placeMetadata.nullable(),
  airport: airportSchema.optional(),
  searchTags: z.string().array().nullish(),
  stateOrCountry: z
    .union([country.partial({ __typename: true }), state.partial({ __typename: true })])
    .optional(),
  thumbnail: z.object({ url: z.string() }).nullish(),
  regionIds: z
    .object({
      items: z.array(regionId),
    })
    .nullish(),
});

export const place = z.union([destination, state, country]);
