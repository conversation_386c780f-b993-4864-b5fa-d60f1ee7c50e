export function removeUtmParams(url: URL | string | undefined) {
  let urlObj;
  if (url && url instanceof URL) {
    urlObj = url;
  } else if (url && typeof url === 'string') {
    urlObj = new URL(url);
  } else {
    return '';
  }
  Array.from(urlObj.searchParams.keys())
    .filter((key) => key.toLowerCase().startsWith('utm_'))
    .forEach((utmKey) => urlObj.searchParams.delete(utmKey));
  return urlObj.toString();
}
