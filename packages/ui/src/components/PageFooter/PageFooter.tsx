import type React from 'react';
import Link from 'next/link';

import type { Tenant } from '@qantasexperiences/tenants';
import { getBrandTitle } from '@qantasexperiences/tenants';

import type { NavigationCollection, NavigationLink } from '../../types/navigation';
import { Divider } from '../Divider';
import { TenantLogo } from '../TenantLogo';
import { BackToTop } from './components/BackToTop';
import { Footer } from './components/Footer';
import { Navigation } from './components/Navigation';
import { COPYRIGHT, LEGAL_LINKS } from './constants';

interface PageFooterProps {
  links?: NavigationLink[];
  navigation?: NavigationCollection[] | null;
  tenant: Tenant;
}

export const PageFooter = ({
  children,
  tenant,
  navigation,
  links = [],
}: React.PropsWithChildren<PageFooterProps>) => {
  const { brand } = tenant;
  const showNavigation = !!navigation?.length;

  return (
    <Footer tenant={tenant}>
      {children}
      <div className="j:bg-[#000236] q:border-t-4 q:border-red-600 q:bg-neutral-800 text-white">
        <div className="container space-y-8 pb-12 sm:space-y-10 sm:pb-16">
          <div>
            <BackToTop />
            <TenantLogo data-testid="tenant-logo" tenant={tenant} inverse className="pt-10 pb-4" />
          </div>
          {showNavigation && <Navigation collections={navigation} />}
          <section className="space-y-8 sm:space-y-10">
            <div className="mx-auto max-w-[50rem] text-center text-base">
              {getBrandTitle(tenant)} would like to acknowledge the Traditional Custodians of the
              local lands and waterways on which we live, work and fly.
              <br />
              We pay our respects to Elders past and present.
            </div>
            <Divider data-testid="divider" decorative color="strong" />
            <div className="flex flex-wrap justify-center gap-4 sm:justify-between">
              <ul className="flex flex-wrap items-center justify-center gap-x-4">
                {LEGAL_LINKS[brand].concat(links).map((link, i) => (
                  <li key={i} className="flex gap-x-4">
                    {i > 0 && (
                      <Divider
                        data-testid="link-divider"
                        decorative
                        orientation="vertical"
                        color="strong"
                      />
                    )}
                    <Link prefetch={false} {...link} className="hover:underline" />
                  </li>
                ))}
              </ul>
              <div>{COPYRIGHT[brand]}</div>
            </div>
          </section>
        </div>
      </div>
    </Footer>
  );
};
