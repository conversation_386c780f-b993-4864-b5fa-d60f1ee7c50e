'use client';

import { useCallback } from 'react';
import jscookie from 'js-cookie';

import { removeLocalStorageItem, setLocalStorageItem } from '@qantasexperiences/utils/browser';

import { LEGACY_SBA_TOKEN_KEY, RELATIONSHIP_ACCESS_TOKEN_COOKIE_NAME } from '~/app/_constants';

type Result = [() => void];

const useSynchronizeAuthentication = (): Result => {
  const synchronizeSBAToken = useCallback(() => {
    /**
     * Token value is procured manually from cookie here, client-side,
     * so the app can set the token value in localStorage,
     * thus enabling authentication in legacy self-service
     */
    const token = jscookie.get(RELATIONSHIP_ACCESS_TOKEN_COOKIE_NAME);

    if (token) {
      setLocalStorageItem(LEGACY_SBA_TOKEN_KEY, token);
    } else {
      removeLocalStorageItem(LEGACY_SBA_TOKEN_KEY);
    }
  }, []);

  return [synchronizeSBAToken];
};

export default useSynchronizeAuthentication;
