import { act, renderHook } from '@testing-library/react';
import jscookie from 'js-cookie';

import { removeLocalStorageItem, setLocalStorageItem } from '@qantasexperiences/utils/browser';

import { LEGACY_SBA_TOKEN_KEY } from '~/app/_constants';
import useSynchronizeAuthentication from './useSynchronizeAuthentication';

jest.mock('js-cookie');
// eslint-disable-next-line @typescript-eslint/unbound-method
const mockGet = jest.mocked(jest.mocked(jscookie.get)); // double jest.mocked due to jscookie.get function overload types not inferred correctly

jest.mock('@qantasexperiences/utils/browser');
const mockRemoveLocalStorageItem = jest.mocked(removeLocalStorageItem);
const mockSetLocalStorageItem = jest.mocked(setLocalStorageItem);

afterEach(() => {
  jest.clearAllMocks();
});

test('initially returns synchronizeSBAToken function', () => {
  const { result } = renderHook(() => useSynchronizeAuthentication());
  const [synchronizeSBAToken] = result.current;

  expect(synchronizeSBAToken).toBeInstanceOf(Function);
});

test('sets SBA authentication data when token exists', () => {
  const token = 'sba-token';
  mockGet.mockReturnValue(token);

  const { result } = renderHook(() => useSynchronizeAuthentication());
  const [synchronizeSBAToken] = result.current;

  act(() => {
    synchronizeSBAToken();
  });

  expect(mockSetLocalStorageItem).toHaveBeenCalledWith(LEGACY_SBA_TOKEN_KEY, token);
});

test('removes SBA authentication data when no token exists', () => {
  mockGet.mockReturnValue(undefined);
  const { result } = renderHook(() => useSynchronizeAuthentication());
  const [synchronizeSBAToken] = result.current;

  act(() => {
    synchronizeSBAToken();
  });

  expect(mockRemoveLocalStorageItem).toHaveBeenCalledWith(LEGACY_SBA_TOKEN_KEY);
});
