'use client';

import type { FC } from 'react';
import { useEffect } from 'react';
import { useRouter } from 'next/navigation';

import PageLoadingState from '~/app/_components/PageLoadingState';
import clientEnv from '~/env/client';
import useSynchronizeAuthentication from './_hooks/useSynchronizeAuthentication';

interface Props {
  path: string;
}

const { NEXT_PUBLIC_LEGACY_SELF_SERVICE_BASE } = clientEnv;

/**
 * This component is a temporary solution for authenticating the old/legacy self-service application
 * and redirecting to the equivalent legacy page,
 * until the new self-service application is fully rolled out.
 *
 * The legacy self-service application relies on localStorage for authenticated API requests.
 */
const RedirectToLegacySelfService: FC<Props> = ({ path }) => {
  const router = useRouter();
  const [synchronizeSBAToken] = useSynchronizeAuthentication();

  useEffect(() => {
    synchronizeSBAToken();
    router.replace(`${NEXT_PUBLIC_LEGACY_SELF_SERVICE_BASE}${path}`);
  }, [path, router, synchronizeSBAToken]);

  return <PageLoadingState />;
};

export default RedirectToLegacySelfService;
