'use client';

import { useCallback, useState } from 'react';

import { removeLocalStorageItem } from '@qantasexperiences/utils/browser';

import { LEGACY_SBA_TOKEN_KEY } from '~/app/_constants';

type Result = [boolean, () => void];

/**
 * This hook exists solely to clear legacy authentication (localStorage) from the app
 * while backwards compatibility with legacy self-service is maintained.
 * See ~/bookings/[bookingId]/_components/RedirectToLegacySelfService/RedirectToLegacySelfService.tsx, where these values are set.
 */
const useClearLegacySelfServiceAuthentication = (): Result => {
  const [isCleared, setIsCleared] = useState(false);

  const clearAuthentication = useCallback(() => {
    removeLocalStorageItem(LEGACY_SBA_TOKEN_KEY);
    setIsCleared(true);
  }, []);

  return [isCleared, clearAuthentication];
};

export default useClearLegacySelfServiceAuthentication;
