import { act, renderHook } from '@testing-library/react';

import { removeLocalStorageItem } from '@qantasexperiences/utils/browser';

import { LEGACY_SBA_TOKEN_KEY } from '~/app/_constants';
import useClearLegacySelfServiceAuthentication from './useClearLegacySelfServiceAuthentication';

jest.mock('@qantasexperiences/utils/browser');
const mockRemoveLocalStorageItem = jest.mocked(removeLocalStorageItem);

beforeEach(() => {
  mockRemoveLocalStorageItem.mockReset();
});

afterEach(() => {
  jest.clearAllMocks();
});

test('initially returns isCleared as false and clearAuthentication function', () => {
  const { result } = renderHook(() => useClearLegacySelfServiceAuthentication());
  const [isCleared, clearAuthentication] = result.current;

  expect(isCleared).toBe(false);
  expect(clearAuthentication).toBeInstanceOf(Function);
});

test('clears legacy SBA authentication data', () => {
  const { result } = renderHook(() => useClearLegacySelfServiceAuthentication());
  const [, clearAuthentication] = result.current;

  act(() => {
    clearAuthentication();
  });

  const [isCleared] = result.current;
  expect(isCleared).toBe(true);
  expect(mockRemoveLocalStorageItem).toHaveBeenCalledWith(LEGACY_SBA_TOKEN_KEY);
});
