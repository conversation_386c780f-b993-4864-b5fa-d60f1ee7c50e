import { cookies } from 'next/headers';
import { redirect, useSearchParams } from 'next/navigation';

import { render, screen, waitFor } from '@qantasexperiences/ui/test-utils';

import { COOKIE_PATH, RELATIONSHIP_ACCESS_TOKEN_COOKIE_NAME } from '~/app/_constants';
import SBALogoutRedirection from './SBALogoutRedirection';

jest.mock('next/headers');
const set = jest.fn();
const mockCookies = jest.mocked(cookies);

jest.mock('next/navigation');
const bookingId = '12345';
const mockUseSearchParams = jest.mocked(useSearchParams);

beforeEach(() => {
  mockCookies.mockResolvedValue({ set } as unknown as Awaited<ReturnType<typeof cookies>>);
  mockUseSearchParams.mockReturnValue(
    new URLSearchParams({ bookingId }) as ReturnType<typeof useSearchParams>,
  );
});

afterEach(() => {
  jest.clearAllMocks();
});

test('renders loading state', async () => {
  render(<SBALogoutRedirection />);

  await waitFor(() => {
    expect(screen.getByText('Please wait...')).toBeInTheDocument();
  });
});

test('clears legacy self-service authentication, relationship authentication and redirects', async () => {
  render(<SBALogoutRedirection />);

  await waitFor(() => {
    expect(set).toHaveBeenCalledWith(RELATIONSHIP_ACCESS_TOKEN_COOKIE_NAME, '', {
      path: COOKIE_PATH,
      maxAge: 0,
      expires: new Date(0),
    });
  });
  expect(redirect).toHaveBeenCalledWith(`/login?bookingId=${bookingId}`);
});
