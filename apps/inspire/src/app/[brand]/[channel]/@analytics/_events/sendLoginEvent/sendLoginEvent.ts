import { sendGTMEvent } from '@next/third-parties/google';

import type { PageType } from '@qantasexperiences/analytics';
import { createLoginEvent, createLoginEventPayload } from '@qantasexperiences/analytics';

export const sendLoginEvent = ({
  pageType,
  userPoints,
  userQffHash,
}: {
  pageType: PageType;
  userPoints: number;
  userQffHash: string;
}) => {
  sendGTMEvent(
    createLoginEvent(
      createLoginEventPayload({
        isLoggedIn: true,
        userId: userQffHash, // keep it same as qffHash to align with the loyalty and qantas.com ecosystem
        userPoints,
        userQffHash,
        pageType,
      }),
    ),
  );
};
