import { sendGTMEvent } from '@next/third-parties/google';

import { ActionType, PageType } from '@qantasexperiences/analytics';

import { sendLoginEvent } from './sendLoginEvent';

jest.mock('@next/third-parties/google');

describe('sendLoginEvent', () => {
  afterEach(() => {
    jest.clearAllMocks();
  });

  it('sends a login GTM event with correct event and event data values', () => {
    const payload = {
      userPoints: 100,
      userQffHash: 'abcde',
      pageType: PageType.ACTIVITIES,
    };

    sendLoginEvent(payload);

    expect(sendGTMEvent).toHaveBeenCalledWith({
      event: 'login',
      event_data: {
        action: ActionType.LOGIN,
        method: 'hard',
        page_type: 'Activities Page',
      },
      user: {
        user_id: 'abcde',
        user_login_status: 'logged in',
        user_points: 100,
        user_qff_hash: 'abcde',
      },
    });
  });
});
