import { randomUUID } from 'crypto';
import { cookies } from 'next/headers';

import { getSessionData } from '@qantasexperiences/auth/server';

import { LoginEvent, PageViewEvent } from './_components';

const QH_PERSISTENT_USER_ID_COOKIE_NAME = 'qh_user_id';
const QFF_HASH_ID_COOKIE_NAME = 'QF_VALUE';

export default async function AnalyticsRoute() {
  const [session, cookieStore] = await Promise.all([getSessionData(), cookies()]);
  const isLoggedIn = !!session?.user;
  const userId = cookieStore.get(QH_PERSISTENT_USER_ID_COOKIE_NAME)?.value ?? randomUUID();
  const qffHashId = cookieStore.get(QFF_HASH_ID_COOKIE_NAME)?.value;

  return (
    <>
      <PageViewEvent
        payload={{
          isLoggedIn,
          userId,
          qffHashId,
          qffPoints: session?.user.pointBalance,
          qffTier: session?.user.membershipTier,
        }}
      />
      {isLoggedIn && qffHashId && (
        <LoginEvent
          payload={{
            userQffHash: qffHashId,
            userPoints: session.user.pointBalance,
          }}
        />
      )}
    </>
  );
}
