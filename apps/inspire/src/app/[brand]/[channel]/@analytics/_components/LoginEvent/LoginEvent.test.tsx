import type { ReadonlyURLSearchParams } from 'next/navigation';
import { usePathname, useRouter } from 'next/navigation';

import { useSearchParams } from '@qantasexperiences/ui';
import { render } from '@qantasexperiences/ui/test-utils';

import { sendLoginEvent } from '../../_events';
import { LoginEvent } from './LoginEvent';

jest.mock('next/navigation');

jest.mock('@qantasexperiences/ui', () => ({
  useSearchParams: jest.fn(),
}));

jest.mock('../../_events', () => ({
  sendLoginEvent: jest.fn(),
}));

describe('LoginEvent', () => {
  let mockUpdateSearchParams: jest.Mock;
  const mockUseRouter = jest.mocked(useRouter);
  const mockUsePathname = jest.mocked(usePathname);
  const mockSendLoginEvent = jest.mocked(sendLoginEvent);

  beforeEach(() => {
    jest.clearAllMocks();
    mockUpdateSearchParams = jest.fn();
    mockUseRouter.mockReturnValue({
      replace: jest.fn(),
      back: jest.fn(),
      forward: jest.fn(),
      refresh: jest.fn(),
      push: jest.fn(),
      prefetch: jest.fn(),
    } as ReturnType<typeof useRouter>);
    mockUsePathname.mockReturnValue('/');

    jest.mocked(useSearchParams).mockImplementation(() => ({
      searchParams: new URLSearchParams() as ReadonlyURLSearchParams,
      updateSearchParams: mockUpdateSearchParams,
    }));
  });

  it('should fire login event and remove loginSuccess param', () => {
    jest.mocked(useSearchParams).mockImplementation(() => ({
      searchParams: new URLSearchParams('loginSuccess=1&foo=bar') as ReadonlyURLSearchParams,
      updateSearchParams: mockUpdateSearchParams,
    }));

    render(<LoginEvent payload={{ userQffHash: 'hash456', userPoints: 1234 }} />);

    expect(mockSendLoginEvent).toHaveBeenCalledWith(
      expect.objectContaining({
        pageType: 'Home Page',
        userPoints: 1234,
        userQffHash: 'hash456',
      }),
    );
    expect(mockUpdateSearchParams).toHaveBeenCalledWith({ loginSuccess: null });
  });

  it('should fire login event with correct page type', () => {
    mockUsePathname.mockReturnValue('/qantas/holidays/activities');
    jest.mocked(useSearchParams).mockImplementation(() => ({
      searchParams: new URLSearchParams('loginSuccess=1&foo=bar') as ReadonlyURLSearchParams,
      updateSearchParams: mockUpdateSearchParams,
    }));

    render(<LoginEvent payload={{ userQffHash: 'hash456', userPoints: 1234 }} />);

    expect(mockSendLoginEvent).toHaveBeenCalledWith(
      expect.objectContaining({
        pageType: 'Activities Page',
      }),
    );
    expect(mockUpdateSearchParams).toHaveBeenCalledWith({ loginSuccess: null });
  });

  it('should not fire login event if loginSuccess param is missing', () => {
    render(<LoginEvent payload={{ userQffHash: 'hash456', userPoints: 1234 }} />);

    expect(mockSendLoginEvent).not.toHaveBeenCalled();
    expect(mockUpdateSearchParams).not.toHaveBeenCalled();
  });
});
