'use client';

import { useEffect } from 'react';
import { usePathname } from 'next/navigation';

import type { CreateLoginEventPayloadOptions } from '@qantasexperiences/analytics';
import { useSearchParams } from '@qantasexperiences/ui';

import { sendLoginEvent } from '../../_events';
import { getPageType } from '../../_utils/getPageType';

interface LoginEventProps {
  payload: Omit<CreateLoginEventPayloadOptions, 'isLoggedIn' | 'pageType' | 'method' | 'userId'>;
}

export const LoginEvent = ({ payload }: LoginEventProps) => {
  const { searchParams, updateSearchParams } = useSearchParams();
  const pathname = usePathname();

  useEffect(() => {
    const hasUserJustLoggedIn = searchParams.get('loginSuccess');
    if (hasUserJustLoggedIn) {
      const { userPoints, userQffHash } = payload;
      const tenantRemovedPathname = `/${pathname.split('/').slice(3).join('/')}`;
      const pageType = getPageType(tenantRemovedPathname);
      sendLoginEvent({
        userPoints,
        userQffHash,
        pageType,
      });
      updateSearchParams({ loginSuccess: null });
    }
  }, [pathname, searchParams, updateSearchParams, payload]);

  return null;
};
