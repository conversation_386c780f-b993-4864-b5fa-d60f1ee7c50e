import { sendGTMEvent } from '@next/third-parties/google';

import { render } from '@qantasexperiences/ui/test-utils';

import { OptimizelyEventTracker } from './OptimizelyEventTracker';

jest.mock('@next/third-parties/google');
const mockSendGTMEvent = jest.mocked(sendGTMEvent);

const decision = {
  variationKey: 'variation1',
  ruleKey: 'rule1',
  enabled: true,
  reasons: [],
  variables: {},
  flagKey: 'test_flag_key',
};

describe('EventTracker', () => {
  beforeEach(() => {
    jest.clearAllMocks();
    Object.defineProperty(window, 'sessionStorage', {
      value: (function () {
        let store: Record<string, string> = {};
        return {
          getItem: (key: string) => store[key] ?? null,
          setItem: (key: string, value: string) => {
            store[key] = value;
          },
          removeItem: (key: string) => {
            delete store[key];
          },
          clear: () => {
            store = {};
          },
        };
      })(),
      writable: true,
    });
  });

  it('renders nothing', () => {
    const { container } = render(
      <OptimizelyEventTracker decision={decision} trackableExperiments={['test_flag_key']} />,
    );

    expect(container).toBeEmptyDOMElement();
  });

  it('does not send event when flagKey is not in tracked list', () => {
    render(
      <OptimizelyEventTracker
        decision={{ ...decision, flagKey: 'not_tracked_flag' }}
        trackableExperiments={['test_flag_key']}
      />,
    );
    expect(mockSendGTMEvent).not.toHaveBeenCalled();
  });

  it('does not send event if already tracked in sessionStorage', () => {
    window.sessionStorage.setItem('optimizely-tracked-test_flag_key', 'true');
    render(<OptimizelyEventTracker decision={decision} trackableExperiments={['test_flag_key']} />);
    expect(mockSendGTMEvent).not.toHaveBeenCalled();
  });

  it('does not send event when decision is undefined', () => {
    render(<OptimizelyEventTracker trackableExperiments={['test_flag_key']} />);

    expect(mockSendGTMEvent).not.toHaveBeenCalled();
  });

  it('sends event when all required fields are present', () => {
    window.sessionStorage.removeItem('optimizely-tracked-test_flag_key');
    render(<OptimizelyEventTracker decision={decision} trackableExperiments={['test_flag_key']} />);
    expect(mockSendGTMEvent).toHaveBeenCalled();
    expect(window.sessionStorage.getItem('optimizely-tracked-test_flag_key')).toBe('true');
  });
});
