'use client';

import { useEffect } from 'react';
import { sendGTMEvent } from '@next/third-parties/google';
import kebabCase from 'lodash/kebabCase';

import {
  createHotelsOptimizelyFeatureDecisionEventPayload,
  createOptimizelyFeatureDecisionEvent,
} from '@qantasexperiences/analytics';

interface OptimizelyEventTrackerProps {
  decision?: {
    enabled: boolean;
    flagKey: string;
    ruleKey: string;
    variationKey: string;
  };
  trackableExperiments?: string[];
}
export const OptimizelyEventTracker = ({
  decision,
  trackableExperiments,
}: OptimizelyEventTrackerProps) => {
  useEffect(() => {
    if (!decision) return;

    const { flagKey, ruleKey, variationKey, enabled } = decision;

    const shouldTrack =
      trackableExperiments?.includes(flagKey) &&
      !sessionStorage.getItem(`optimizely-tracked-${flagKey}`);

    if (shouldTrack) {
      const fxpVariantString = [flagKey, variationKey, ruleKey].map(kebabCase).join('_');
      const enabledString = String(enabled);

      const eventPayload = createHotelsOptimizelyFeatureDecisionEventPayload({
        fxp_variant_string: fxpVariantString,
        enabled: enabledString,
      });

      const optimizelyEvent = createOptimizelyFeatureDecisionEvent(eventPayload);

      sendGTMEvent(optimizelyEvent);

      sessionStorage.setItem(`optimizely-tracked-${flagKey}`, 'true');
    }
  }, [decision, trackableExperiments]);

  return null;
};
