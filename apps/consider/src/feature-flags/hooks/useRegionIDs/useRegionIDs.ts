import { useIncreasedRange } from '@qantasexperiences/optimizely/flag-hooks';

export interface DestinationMinimal {
  airport: { code: string };
  name: string;
  regionIds?: number[] | null | undefined;
  slug: string;
}

export enum VariationKeys {
  ENABLE_ALL_REGIONS = 'enable_all_regions',
  ENABLE_SOME_REGIONS = 'enable_some_regions',
  OFF = 'off',
}

/**
 * Determines the regionIds based on the feature flag conditions
 * @param destination - The destination object containing regionIds
 * @returns regionIds array or null based on feature flag conditions
 */
export const useRegionIDs = (destination: DestinationMinimal | undefined): number[] | null => {
  const increasedRange = useIncreasedRange();

  if (!increasedRange?.enabled || !destination?.regionIds) {
    return null;
  }

  const { variationKey, variables } = increasedRange;

  if (variationKey === VariationKeys.ENABLE_ALL_REGIONS) {
    return destination.regionIds.length > 0 ? destination.regionIds : null;
  }

  if (variationKey === VariationKeys.ENABLE_SOME_REGIONS) {
    const allowedRegions = variables.regions;
    if (Object.keys(allowedRegions).length === 0) {
      return null;
    }

    const allowedRegionValues = Object.values(allowedRegions).flat();
    const filteredRegionIds = destination.regionIds.filter((regionId) => {
      return allowedRegionValues.includes(regionId);
    });

    return filteredRegionIds.length > 0 ? filteredRegionIds : null;
  }

  return null;
};
