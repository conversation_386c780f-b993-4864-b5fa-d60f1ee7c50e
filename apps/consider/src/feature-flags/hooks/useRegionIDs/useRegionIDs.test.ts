import { useIncreasedRange } from '@qantasexperiences/optimizely/flag-hooks';
import { renderHook } from '@qantasexperiences/ui/test-utils';

import type { DestinationMinimal } from './useRegionIDs';
import { useRegionIDs, VariationKeys } from './useRegionIDs';

jest.mock('@qantasexperiences/optimizely/flag-hooks', () => ({
  useIncreasedRange: jest.fn(),
}));

describe('useRegionIDs', () => {
  const baseDestinationMock = {
    airport: { code: 'BNE' },
    name: 'Brisbane',
    slug: 'brisbane',
  } satisfies DestinationMinimal;

  const mockIncreasedRange = {
    flagKey: 'eja-increase-range',
    reasons: [],
    ruleKey: 'regions_on_for_qantas',
    variables: {
      regions: {},
    },
    enabled: true,
    variationKey: VariationKeys.OFF,
  };

  beforeEach(() => {
    jest.clearAllMocks();
  });

  it('should return null when feature flag is undefined', () => {
    jest.mocked(useIncreasedRange).mockReturnValue(undefined);

    const { result } = renderHook(() => useRegionIDs(baseDestinationMock));
    expect(result.current).toBeNull();
  });

  it('should return null when feature flag is disabled', () => {
    jest
      .mocked(useIncreasedRange)
      .mockReturnValue({ ...mockIncreasedRange, enabled: false, variationKey: VariationKeys.OFF });

    const { result } = renderHook(() => useRegionIDs(baseDestinationMock));
    expect(result.current).toBeNull();
  });

  describe('when feature flag is enabled with enable_all_regions variation', () => {
    beforeEach(() => {
      jest.mocked(useIncreasedRange).mockReturnValue({
        ...mockIncreasedRange,
        enabled: true,
        variationKey: VariationKeys.ENABLE_ALL_REGIONS,
      });
    });

    it('should return all regionIds from destination', () => {
      const { result } = renderHook(() =>
        useRegionIDs({
          ...baseDestinationMock,
          regionIds: [51819, 51820, 51821],
        }),
      );
      expect(result.current).toEqual([51819, 51820, 51821]);
    });

    it('should return null when destination has no regionIds', () => {
      const { result } = renderHook(() =>
        useRegionIDs({
          ...baseDestinationMock,
          regionIds: null,
        }),
      );
      expect(result.current).toBeNull();
    });

    it('should return null when destination has empty regionIds array', () => {
      const { result } = renderHook(() =>
        useRegionIDs({
          ...baseDestinationMock,
          regionIds: [],
        }),
      );
      expect(result.current).toBeNull();
    });

    it('should return null when destination is undefined', () => {
      const { result } = renderHook(() => useRegionIDs(undefined));
      expect(result.current).toBeNull();
    });
  });

  describe('when feature flag is enabled with enable_some_regions variation', () => {
    it('should return filtered regionIds that match feature flag variables', () => {
      jest.mocked(useIncreasedRange).mockReturnValue({
        ...mockIncreasedRange,
        enabled: true,
        variables: {
          regions: {
            'Airlie Beach': 51819,
            'Gold Coast': 51820,
          },
        },
        variationKey: VariationKeys.ENABLE_SOME_REGIONS,
      });

      const { result } = renderHook(() =>
        useRegionIDs({
          ...baseDestinationMock,
          regionIds: [51819, 51820, 51821],
        }),
      );
      expect(result.current).toEqual([51819, 51820]);
    });

    it('should return null when no regionIds match feature flag variables', () => {
      jest.mocked(useIncreasedRange).mockReturnValue({
        ...mockIncreasedRange,
        enabled: true,
        variables: {
          regions: {
            Sydney: 51822,
            Melbourne: 51823,
          },
        },
        variationKey: VariationKeys.ENABLE_SOME_REGIONS,
      });

      const { result } = renderHook(() =>
        useRegionIDs({
          ...baseDestinationMock,
          regionIds: [51819, 51820, 51821],
        }),
      );
      expect(result.current).toBeNull();
    });

    it('should return null when feature flag has no regions variable', () => {
      jest.mocked(useIncreasedRange).mockReturnValue({
        ...mockIncreasedRange,
        enabled: true,
        variables: {
          regions: {},
        },
        variationKey: VariationKeys.ENABLE_SOME_REGIONS,
      });

      const { result } = renderHook(() =>
        useRegionIDs({
          ...baseDestinationMock,
          regionIds: [51819, 51820, 51821],
        }),
      );
      expect(result.current).toBeNull();
    });

    it('should return null when destination has no regionIds', () => {
      jest.mocked(useIncreasedRange).mockReturnValue({
        ...mockIncreasedRange,
        enabled: true,
        variables: {
          regions: {
            'Airlie Beach': 51819,
          },
        },
        variationKey: VariationKeys.ENABLE_SOME_REGIONS,
      });

      const { result } = renderHook(() =>
        useRegionIDs({
          ...baseDestinationMock,
          regionIds: null,
        }),
      );
      expect(result.current).toBeNull();
    });

    it('should combine ids from multiple regions into a single array', () => {
      jest.mocked(useIncreasedRange).mockReturnValue({
        ...mockIncreasedRange,
        enabled: true,
        variables: {
          regions: {
            'Airlie Beach': 51819,
            Melbourne: [51820, 51821],
          },
        },
        variationKey: VariationKeys.ENABLE_SOME_REGIONS,
      });

      const { result } = renderHook(() =>
        useRegionIDs({
          ...baseDestinationMock,
          regionIds: [51819, 51820, 51821],
        }),
      );
      expect(result.current).toEqual([51819, 51820, 51821]);
    });
  });
});
