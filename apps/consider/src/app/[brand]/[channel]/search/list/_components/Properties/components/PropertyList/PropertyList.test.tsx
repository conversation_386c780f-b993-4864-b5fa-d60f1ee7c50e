import type { SearchPageSanityCampaign } from '@qantasexperiences/sanity/api';
import type { CustomRenderOptions } from '@qantasexperiences/ui/test-utils';
import { render, screen } from '@qantasexperiences/ui/test-utils';

import type { SearchListProperty } from '../../../../_hooks/useGetProperties';
import type { PropertyListProps } from './PropertyList';
import { usePricingStrategy } from '~/feature-flags/hooks';
import { PropertyList } from './PropertyList';

jest.mock('next/navigation', () => ({
  useSearchParams: jest.fn().mockReturnValue(new URLSearchParams()),
  usePathname: jest.fn().mockReturnValue('/test/route'),
}));

jest.mock('~/feature-flags/hooks');

const renderPropertyList = (props: PropertyListProps, options?: CustomRenderOptions) =>
  render(<PropertyList {...props} />, {
    brand: 'qantas',
    channel: 'holidays',
    ...options,
  });

const campaign = {
  endDate: '2026-12-14T13:37:00Z',
  calendar: {
    title: 'Return for FREE',
    noOriginMessage: "Please select where you'll be departing from to see travel dates",
    unsupportedRouteMessage:
      "Unfortunately Return for FREE fares are not available from where you'll be departing, please update your search and try again",
  },
  flights: [
    {
      destination: 'MEL',
      origin: 'ADL',
      travelDates: [
        {
          start: '2023-07-25',
          end: '2023-09-13',
        },
        {
          start: '2023-10-17',
          end: '2023-11-14',
        },
        {
          start: '2024-01-09',
          end: '2024-02-29',
        },
      ],
    },
  ],
  activeInAllDestinations: false,
  activeDestinations: [
    'port-douglas',
    'cairns',
    'uluru',
    'hamilton-island',
    'queenstown',
    'launceston',
    'palm-cove',
    'hobart',
    'townsville',
    'darwin',
    'brisbane',
    'melbourne',
    'gold-coast',
  ],
  sashing: {
    flightPromoCode: 'RFF',
    flightPillText: 'Return For Free',
  },
  banner: {
    termsConditions: 'TEST222',
    text: 'Return For FREE^ - sale now on! Select where you’ll be departing from and check calendar for travel dates.',
  },
} satisfies SearchPageSanityCampaign;

const PROPERTY = {
  id: '1',
  destinationName: 'Hamilton Island & Whitsundays',
  name: 'InterContinental Hayman Island Resort, an IHG Hotel',
  rating: 4,
  ratingType: 'AAA',
  tags: ['Luxury', 'Food and wine', 'Island escape'],
  tagline:
    'Immerse yourself at this luxurious stay in the heart of the Great Barrier Reef. Featuring panoramic seascapes and tropical gardens.',
  mainImage: {
    urlLarge: 'src',
    caption: 'src',
  },
  images: [{ urlLarge: 'src', caption: 'src' }],
  offer: { charges: { total: '199' }, propertyId: '1', offerId: '2', roomTypeName: 'standard' },
} satisfies SearchListProperty;

const DEFAULT_CUSTOMER_QUERY = {
  departureDate: '2024-06-26',
  returnDate: '2024-06-27',
  adults: 2,
  children: 0,
  infants: 0,
  destinationName: 'sydney',
  destinationCode: 'SYD',
  originCode: 'MEL',
  limit: 20,
  page: 1,
};

describe('PropertyList', () => {
  beforeEach(() => {
    jest.mocked(usePricingStrategy).mockReturnValue('per-person');
  });

  it('shows a property card for each property', () => {
    renderPropertyList({
      properties: [
        PROPERTY,
        { ...PROPERTY, id: '2' },
        { ...PROPERTY, id: '3' },
        { ...PROPERTY, id: '4' },
      ],
      origins: [],
      customerQuery: DEFAULT_CUSTOMER_QUERY,
    });

    expect(screen.getAllByTestId('property-card')).toHaveLength(4);
  });

  it('shows a campaign badge for each property if campaign is provided for jetstar', () => {
    renderPropertyList(
      {
        properties: [
          PROPERTY,
          { ...PROPERTY, id: '2' },
          { ...PROPERTY, id: '3' },
          { ...PROPERTY, id: '4' },
        ],
        matchingFlightCampaign: campaign,
        origins: [],
        customerQuery: DEFAULT_CUSTOMER_QUERY,
      },
      { brand: 'jetstar' },
    );

    const badges = screen.getAllByTestId('badge');

    expect(badges).toHaveLength(4);

    badges.forEach((badge) => expect(badge).toHaveTextContent('Return For Free'));
  });

  it('shows no campaign badges for qantas even when provided a campaign', () => {
    renderPropertyList(
      {
        properties: [
          PROPERTY,
          { ...PROPERTY, id: '2' },
          { ...PROPERTY, id: '3' },
          { ...PROPERTY, id: '4' },
        ],
        matchingFlightCampaign: campaign,
        origins: [],
        customerQuery: DEFAULT_CUSTOMER_QUERY,
      },
      { brand: 'qantas' },
    );

    const badges = screen.queryAllByText('Return For Free');

    expect(badges).toHaveLength(0);
  });

  it('shows Select package button for Qantas Holidays with correct package URL', () => {
    renderPropertyList({
      properties: [PROPERTY],
      origins: [],
      customerQuery: DEFAULT_CUSTOMER_QUERY,
    });

    const selectButton = screen.getByRole('link', { name: 'Select package' });
    expect(selectButton).toHaveAttribute(
      'href',
      'https://sit-qp-customer-ui.qantashotels.com/holidays/sydney/properties/1/package?originCode=MEL&departureDate=2024-06-26&returnDate=2024-06-27&adults=2&children=0&infants=0&packageOption=best-rate',
    );
  });

  it('shows Select package button for Jetstar Holidays with correct package URL', () => {
    renderPropertyList(
      {
        properties: [PROPERTY],
        origins: [],
        customerQuery: DEFAULT_CUSTOMER_QUERY,
      },
      { brand: 'jetstar', channel: 'holidays' },
    );

    const selectButton = screen.getByRole('link', { name: 'Select package' });
    expect(selectButton).toHaveAttribute(
      'href',
      'https://sit-jetstar-holidays-ui.qantashotels.com/au/en/holidays/destinations/sydney/properties/1/package?originCode=MEL&departureDate=2024-06-26&returnDate=2024-06-27&adults=2&children=0&infants=0&packageOption=best-rate',
    );
  });

  it("renders the per-person price point variant when the pricing strategy is 'per-person'", () => {
    jest.mocked(usePricingStrategy).mockReturnValue('per-person');

    renderPropertyList(
      {
        properties: [PROPERTY],
        origins: [],
        customerQuery: DEFAULT_CUSTOMER_QUERY,
      },
      { brand: 'jetstar', channel: 'holidays' },
    );

    expect(screen.getByTestId('price-cash')).toHaveTextContent('$100 /person^');
  });

  it("renders the total price point variant when the pricing strategy is 'total'", () => {
    jest.mocked(usePricingStrategy).mockReturnValue('total');

    renderPropertyList(
      {
        properties: [PROPERTY],
        origins: [],
        customerQuery: DEFAULT_CUSTOMER_QUERY,
      },
      { brand: 'jetstar', channel: 'holidays' },
    );

    expect(screen.getByTestId('price-cash')).toHaveTextContent('$199^');
  });
});
