import { render, renderHook } from '@qantasexperiences/ui/test-utils';

import type { PropertiesSearchProviderProps } from './PropertiesSearchProvider';
import { useGetProperties } from '../../_hooks';
import { PropertiesSearchProvider, usePropertiesSearch } from './PropertiesSearchProvider';

jest.mock('../../_hooks', () => ({
  useGetProperties: jest.fn(),
}));

const DEFAULT_DATA = {
  propertiesWithOffer: [],
  promoCodes: ['TEST'],
  meta: {
    searchOptions: {
      adults: 1,
      children: 0,
      departureDate: '2030-01-01',
      destinationCode: 'SYD',
      destinationName: 'sydney',
      infants: 0,
      limit: 20,
      originCode: 'MEL',
      page: 2,
      returnDate: '2030-01-08',
    },
    totalPackageOffers: 92,
    minPrice: 150,
    maxPrice: 2500,
  },
};

const DEFAULT_GET_PROPERTIES_RETURN = {
  data: DEFAULT_DATA,
  isLoading: false,
  searchPanelStateForCurrentResponse: undefined,
};

const DESTINATIONS = [
  {
    airport: { code: 'SYD' },
    slug: 'sydney',
    name: 'Sydney',
    regionIds: [100, 200],
  },
  {
    airport: { code: 'MEL' },
    slug: 'melbourne',
    name: 'Melbourne',
    regionIds: [300],
  },
] satisfies PropertiesSearchProviderProps['destinations'];

describe('PropertiesSearchContext', () => {
  beforeEach(() => {
    jest.mocked(useGetProperties).mockReturnValue(DEFAULT_GET_PROPERTIES_RETURN);
  });

  it('should call useGetProperties with the provided destinations', () => {
    render(
      <PropertiesSearchProvider destinations={DESTINATIONS}>
        Test Children
      </PropertiesSearchProvider>,
    );

    expect(useGetProperties).toHaveBeenCalledWith({ destinations: DESTINATIONS });
  });

  it('should provide the result of useGetProperties through the context via usePropertiesSearch hook', () => {
    jest.mocked(useGetProperties).mockReturnValue(DEFAULT_GET_PROPERTIES_RETURN);

    const { result } = renderHook(() => usePropertiesSearch(), {
      wrapper: ({ children }) => (
        <PropertiesSearchProvider destinations={DESTINATIONS}>{children}</PropertiesSearchProvider>
      ),
    });

    expect(result.current).toBe(DEFAULT_GET_PROPERTIES_RETURN);
  });
});
