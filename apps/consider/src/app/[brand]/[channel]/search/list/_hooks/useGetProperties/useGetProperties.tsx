'use client';

import type {
  HolidaysPropertiesQueryResult,
  HolidaysPropertyWithOffer,
  UseHolidaysPropertiesWithOfferReturn,
} from '@qantasexperiences/data/client';
import type { SearchPanelStateValues } from '@qantasexperiences/ui';
import { useHolidaysPropertiesWithOffer } from '@qantasexperiences/data/client';
import {
  useHolidaysSearchFilterState,
  useHolidaysSearchSortState,
  useSearchPanelState,
} from '@qantasexperiences/ui';

import { useRegionIDs } from '~/feature-flags/hooks';
import { sendSearchErrorEvent } from '../../_analytics';
import { PROPERTIES_PER_PAGE } from '../../_constants';
import { usePropertiesPagination } from '../usePropertiesPagination';
import {
  addHalfStarRatings,
  getSearchPanelStateFromSearchOptions,
  hasSearchConnectionError,
} from './utils';
import { addDestinationNameToProperties } from './utils/addDestinationNameToProperties';

export interface DestinationMinimal {
  airport: { code: string };
  name: string;
  regionIds?: number[] | null | undefined;
  slug: string;
}

export interface UseGetPropertiesOptions {
  destinations: DestinationMinimal[];
}

export type SearchListProperty = HolidaysPropertyWithOffer & {
  destinationName: string;
};
export type HolidaysPropertiesData = HolidaysPropertiesQueryResult<SearchListProperty>;

export type UseGetPropertiesReturn = Omit<UseHolidaysPropertiesWithOfferReturn, 'data'> & {
  data: HolidaysPropertiesData | undefined;
  searchPanelStateForCurrentResponse: SearchPanelStateValues | undefined;
};

export const useGetProperties = ({
  destinations,
}: UseGetPropertiesOptions): UseGetPropertiesReturn => {
  const [searchPanelState] = useSearchPanelState();
  const { searchFilterState } = useHolidaysSearchFilterState();
  const [sortState] = useHolidaysSearchSortState();
  const { page } = usePropertiesPagination();

  const destination = destinations.find(({ slug }) => slug === searchPanelState.destination);

  const regionIds = useRegionIDs(destination);

  const response = useHolidaysPropertiesWithOffer(
    {
      ...searchPanelState,
      destinationName: destination?.slug,
      destinationCode: destination?.airport.code,
      minOfferPriceAud: searchFilterState.minPrice,
      maxOfferPriceAud: searchFilterState.maxPrice,
      propertyRatings: addHalfStarRatings(searchFilterState.propertyRatings),
      page,
      limit: PROPERTIES_PER_PAGE,
      sort: sortState ?? 'default',
      regionIds,
    },
    {
      onQueryStart: () => {
        window.scrollTo({ top: 0, behavior: 'instant' });
      },
      onError: (error, { searchOptions }) => {
        // This implementation is used for ZScalar issue
        // where the API call timeouts with ERR_CONNECTION_CLOSE
        if (hasSearchConnectionError(error)) {
          sendSearchErrorEvent({
            errors: [{ message: 'No response object', name: 'No response object' }],
            searchTerm: searchOptions.destinationName,
          });
        }
      },
    },
  );

  const searchPanelStateForCurrentResponse = getSearchPanelStateFromSearchOptions(
    response.data?.meta.searchOptions,
  );

  const dataWithDestinationName = addDestinationNameToProperties({
    data: response.data,
    destinationSlug: searchPanelStateForCurrentResponse?.destination,
    destinations,
  });

  return {
    searchPanelStateForCurrentResponse,
    ...{
      ...response,
      data: dataWithDestinationName,
    },
  };
};
