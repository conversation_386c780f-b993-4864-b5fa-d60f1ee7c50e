import { useHolidaysPropertiesWithOffer } from '@qantasexperiences/data/client';
import {
  useHolidaysSearchFilterState,
  useHolidaysSearchSortState,
  useSearchPanelState,
} from '@qantasexperiences/ui';
import { renderHook } from '@qantasexperiences/ui/test-utils';

import { useRegionIDs } from '~/feature-flags/hooks';
import { sendSearchErrorEvent } from '../../_analytics';
import { hilton, oakwood } from '../../_components/Properties/_mocks';
import { PROPERTIES_PER_PAGE } from '../../_constants';
import { usePropertiesPagination } from '../usePropertiesPagination';
import { useGetProperties } from './useGetProperties';

jest.mock('@qantasexperiences/ui', () => {
  const originalModule =
    jest.requireActual<typeof import('@qantasexperiences/ui')>('@qantasexperiences/ui');

  return {
    ...originalModule,
    useSearchPanelState: jest.fn(),
    useHolidaysSearchFilterState: jest.fn(),
    useHolidaysSearchSortState: jest.fn(),
  };
});
jest.mock('@qantasexperiences/data/client');
jest.mock('../usePropertiesPagination');
jest.mock('~/feature-flags/hooks', () => ({
  useRegionIDs: jest.fn(),
}));
jest.mock('../../_analytics');

const DEFAULT_DATA = {
  propertiesWithOffer: [hilton, oakwood],
  promoCodes: ['TEST'],
  meta: {
    searchOptions: {
      adults: 1,
      children: 0,
      departureDate: '2030-01-01',
      destinationCode: 'SYD',
      destinationName: 'sydney',
      infants: 0,
      limit: 20,
      originCode: 'MEL',
      page: 2,
      returnDate: '2030-01-08',
    },
    totalPackageOffers: 92,
    minPrice: 150,
    maxPrice: 2500,
  },
};

const DEFAULT_SEARCH_PANEL_STATE = {
  adults: 1,
  children: 0,
  departureDate: '2030-01-01',
  destination: 'sydney',
  infants: 0,
  originCode: 'MEL',
  returnDate: '2030-01-08',
};

const DEFAULT_FILTER_STATE = {
  minPrice: null,
  maxPrice: null,
  propertyRatings: null,
};

const DEFAULT_FILTER_HOOK_RETURN = {
  searchFilterState: DEFAULT_FILTER_STATE,
  setSearchFilterState: jest.fn(),
  clearFilterState: jest.fn(),
  isAnyFilterApplied: false,
};

const DEFAULT_SORT_STATE = null;

const DEFAULT_PROPERTIES_PAGINATION = {
  page: 1,
  getPageHref: jest.fn(),
  setPage: jest.fn(),
};

describe('useGetProperties', () => {
  const destinations = [
    {
      slug: 'canberra',
      name: 'Canberra',
      airport: { code: 'CBR' },
      regionIds: [1234],
    },
  ];
  const renderUseGetProperties = () => renderHook(() => useGetProperties({ destinations }));

  beforeEach(() => {
    jest.mocked(useSearchPanelState).mockReturnValue([DEFAULT_SEARCH_PANEL_STATE, jest.fn()]);
    jest.mocked(useHolidaysSearchFilterState).mockReturnValue(DEFAULT_FILTER_HOOK_RETURN);
    jest.mocked(useHolidaysSearchSortState).mockReturnValue([DEFAULT_SORT_STATE, jest.fn()]);
    jest.mocked(usePropertiesPagination).mockReturnValue(DEFAULT_PROPERTIES_PAGINATION);
    jest
      .mocked(useHolidaysPropertiesWithOffer)
      .mockReturnValue({ data: DEFAULT_DATA, isLoading: false });
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  it('calls useHolidaysPropertiesWithOffer with destination, search panel state and pagination page/limit', () => {
    jest
      .mocked(useHolidaysPropertiesWithOffer)
      .mockReturnValue({ data: DEFAULT_DATA, isLoading: false });
    jest.mocked(useSearchPanelState).mockReturnValue([
      {
        adults: 1,
        children: 0,
        infants: 0,
        departureDate: '2030-01-01',
        originCode: 'MEL',
        returnDate: '2030-01-08',
        destination: 'canberra',
      },
      jest.fn(),
    ]);
    jest.mocked(usePropertiesPagination).mockReturnValue({
      ...DEFAULT_PROPERTIES_PAGINATION,
      page: 3,
    });
    jest.mocked(useRegionIDs).mockReturnValue([1234]);
    renderUseGetProperties();

    expect(useHolidaysPropertiesWithOffer).toHaveBeenCalledWith(
      expect.objectContaining({
        adults: 1,
        children: 0,
        infants: 0,
        departureDate: '2030-01-01',
        originCode: 'MEL',
        returnDate: '2030-01-08',
        destinationName: 'canberra',
        destinationCode: 'CBR',
        page: 3,
        limit: PROPERTIES_PER_PAGE,
        regionIds: [1234],
      }),
      expect.anything(),
    );
  });

  it('calls useHolidaysPropertiesWithOffer with region IDs as undefined when there are no regions', () => {
    jest
      .mocked(useHolidaysPropertiesWithOffer)
      .mockReturnValue({ data: DEFAULT_DATA, isLoading: false });
    jest.mocked(useRegionIDs).mockReturnValue(null);
    renderHook(() =>
      useGetProperties({
        destinations: [
          {
            slug: 'sydney',
            airport: { code: 'SYD' },
            name: 'Sydney',
            regionIds: [],
          },
        ],
      }),
    );

    expect(useHolidaysPropertiesWithOffer).toHaveBeenCalledWith(
      expect.objectContaining({
        regionIds: null,
      }),
      expect.anything(),
    );
  });

  it('calls useHolidaysPropertiesWithOffer without region IDs when feature flag is off', () => {
    jest
      .mocked(useHolidaysPropertiesWithOffer)
      .mockReturnValue({ data: DEFAULT_DATA, isLoading: false });
    jest.mocked(useSearchPanelState).mockReturnValue([
      {
        adults: 1,
        children: 0,
        infants: 0,
        departureDate: '2030-01-01',
        originCode: 'MEL',
        returnDate: '2030-01-08',
        destination: 'canberra',
      },
      jest.fn(),
    ]);
    jest.mocked(useRegionIDs).mockReturnValue(null);

    renderUseGetProperties();

    expect(useHolidaysPropertiesWithOffer).toHaveBeenCalledWith(
      expect.not.objectContaining({
        regionIds: [1234],
      }),
      expect.anything(),
    );
  });

  it('calls useHolidaysPropertiesWithOffer with filter state when it is present', () => {
    jest
      .mocked(useHolidaysPropertiesWithOffer)
      .mockReturnValue({ data: DEFAULT_DATA, isLoading: false });
    jest.mocked(useHolidaysSearchFilterState).mockReturnValue({
      ...DEFAULT_FILTER_HOOK_RETURN,
      searchFilterState: {
        minPrice: 100,
        maxPrice: 500,
        propertyRatings: [5],
      },
    });

    renderUseGetProperties();

    expect(useHolidaysPropertiesWithOffer).toHaveBeenCalledWith(
      expect.objectContaining({
        minOfferPriceAud: 100,
        maxOfferPriceAud: 500,
        propertyRatings: [5],
      }),
      expect.anything(),
    );
  });

  it('adds half star ratings to ratings below 5 when calling useHolidaysPropertiesWithOffer', () => {
    jest
      .mocked(useHolidaysPropertiesWithOffer)
      .mockReturnValue({ data: DEFAULT_DATA, isLoading: false });
    jest.mocked(useHolidaysSearchFilterState).mockReturnValue({
      ...DEFAULT_FILTER_HOOK_RETURN,
      searchFilterState: { ...DEFAULT_FILTER_STATE, propertyRatings: [3, 4, 5] },
    });

    renderUseGetProperties();

    expect(useHolidaysPropertiesWithOffer).toHaveBeenCalledWith(
      expect.objectContaining({
        propertyRatings: [3, 3.5, 4, 4.5, 5],
      }),
      expect.anything(),
    );
  });

  it('calls useHolidaysPropertiesWithOffer with sort state when it is present', () => {
    jest
      .mocked(useHolidaysPropertiesWithOffer)
      .mockReturnValue({ data: DEFAULT_DATA, isLoading: false });
    jest.mocked(useHolidaysSearchSortState).mockReturnValue(['price_asc', jest.fn()]);

    renderUseGetProperties();

    expect(useHolidaysPropertiesWithOffer).toHaveBeenCalledWith(
      expect.objectContaining({
        sort: 'price_asc',
      }),
      expect.anything(),
    );
  });

  it('calls useHolidaysPropertiesWithOffer with sort as "default" when sort state is null', () => {
    jest
      .mocked(useHolidaysPropertiesWithOffer)
      .mockReturnValue({ data: DEFAULT_DATA, isLoading: false });
    jest.mocked(useHolidaysSearchSortState).mockReturnValue([null, jest.fn()]);

    renderUseGetProperties();

    expect(useHolidaysPropertiesWithOffer).toHaveBeenCalledWith(
      expect.objectContaining({
        sort: 'default',
      }),
      expect.anything(),
    );
  });

  it('returns the results of useHolidaysPropertiesWithOffer with destinationName added to the propertiesWithOffer', () => {
    jest
      .mocked(useHolidaysPropertiesWithOffer)
      .mockReturnValue({ data: DEFAULT_DATA, isLoading: false });
    const { result } = renderHook(() =>
      useGetProperties({
        destinations: [
          {
            slug: 'sydney',
            airport: { code: 'SYD' },
            name: 'Sydney',
            regionIds: [],
          },
        ],
      }),
    );

    expect(result.current.data).toEqual({
      ...DEFAULT_DATA,
      propertiesWithOffer: [
        { ...hilton, destinationName: 'Sydney' },
        { ...oakwood, destinationName: 'Sydney' },
      ],
    });
  });

  it('returns no offers if the searched destination is not in the list', () => {
    jest
      .mocked(useHolidaysPropertiesWithOffer)
      .mockReturnValue({ data: DEFAULT_DATA, isLoading: false });
    const { result } = renderUseGetProperties();

    expect(result.current.data).toBeUndefined();
  });

  it('returns the search panel state used to get the current results based on the returned meta', () => {
    jest.mocked(useHolidaysPropertiesWithOffer).mockReturnValue({
      data: {
        ...DEFAULT_DATA,
        meta: {
          ...DEFAULT_DATA.meta,
          searchOptions: {
            adults: 4,
            children: 1,
            departureDate: '2031-02-01',
            destinationName: 'bali',
            destinationCode: 'DPS',
            infants: 0,
            originCode: 'MEL',
            returnDate: '2031-02-08',
            limit: 20,
            page: 1,
          },
        },
      },
      isLoading: false,
    });

    const { result } = renderUseGetProperties();

    expect(result.current.searchPanelStateForCurrentResponse).toStrictEqual({
      adults: 4,
      children: 1,
      departureDate: '2031-02-01',
      destination: 'bali',
      infants: 0,
      originCode: 'MEL',
      returnDate: '2031-02-08',
    });
  });

  it('calls sendSearchErrorEvent when onError is triggered with a connection closed error and a valid destination', () => {
    const mockSendSearchErrorEvent = jest.mocked(sendSearchErrorEvent);

    jest.mocked(useSearchPanelState).mockReturnValue([
      {
        ...DEFAULT_SEARCH_PANEL_STATE,
        destination: 'canberra',
      },
      jest.fn(),
    ]);

    jest.mocked(useHolidaysPropertiesWithOffer).mockImplementation((params, options) => {
      if (options?.onError) {
        const connectionError = new Error('net::ERR_CONNECTION_CLOSED');
        const searchOptions = {
          adults: 1,
          children: 0,
          departureDate: '2030-01-01',
          destinationCode: 'CBR',
          destinationName: 'canberra',
          infants: 0,
          limit: 20,
          originCode: 'MEL',
          page: 1,
          returnDate: '2030-01-08',
        };
        options.onError(connectionError, { searchOptions });
      }
      return { data: DEFAULT_DATA, isLoading: false };
    });

    renderUseGetProperties();

    expect(mockSendSearchErrorEvent).toHaveBeenCalledWith({
      errors: [{ message: 'No response object', name: 'No response object' }],
      searchTerm: 'canberra',
    });
  });
});
